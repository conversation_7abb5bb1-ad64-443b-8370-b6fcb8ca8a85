import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  unique,
  uuid,
  json,
  pgEnum,
  numeric,
  doublePrecision
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users';

// Enums related to pharmacy and prescriptions
export const enumPharmacyStatus = pgEnum('enum_pharmacy_status', [
  'ACTIVE',
  'INACTIVE'
]);

export const enumPrescriptionStatus = pgEnum('enum_prescription_status', [
  'PENDING',
  'SENT',
  'DELIVERED',
  'CANCELLED'
]);

export const enumDrugType = pgEnum('enum_drug_type', [
  'BRAND',
  'GENERIC',
  'OTC'
]);

// Pharmacy table
export const pharmacy = pgTable(
  'pharmacy',
  {
    pharmacyId: serial('pharmacy_id').primaryKey().notNull(),
    pharmacyName: varchar('pharmacy_name', { length: 255 }).notNull(),
    pharmacyAddress: text('pharmacy_address'),
    pharmacyCity: varchar('pharmacy_city', { length: 100 }),
    pharmacyState: varchar('pharmacy_state', { length: 50 }),
    pharmacyZip: varchar('pharmacy_zip', { length: 20 }),
    pharmacyPhone: varchar('pharmacy_phone', { length: 20 }),
    pharmacyFax: varchar('pharmacy_fax', { length: 20 }),
    pharmacyEmail: varchar('pharmacy_email', { length: 255 }),
    ncpdpId: varchar('ncpdp_id', { length: 20 }),
    npi: varchar({ length: 20 }),
    status: enumPharmacyStatus().default('ACTIVE'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    dosespotPharmacyId: varchar('dosespot_pharmacy_id', { length: 200 }),
    latitude: doublePrecision(),
    longitude: doublePrecision(),
    distance: doublePrecision(),
    hours: json(),
    services: json(),
    isPreferred: boolean('is_preferred').default(false),
    chainId: varchar('chain_id', { length: 50 }),
    chainName: varchar('chain_name', { length: 255 })
  }
);

// User Pharmacy Preferences table
export const userPharmacyPreferences = pgTable(
  'user_pharmacy_preferences',
  {
    userPharmacyPreferenceId: serial('user_pharmacy_preference_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id').notNull(),
    pharmacyId: integer('pharmacy_id').notNull(),
    isPreferred: boolean('is_preferred').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_pharmacy_preferences_user_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacy.pharmacyId],
      name: 'user_pharmacy_preferences_pharmacy_id_fkey'
    })
  ]
);

// Drugs table
export const drugs = pgTable(
  'drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    drugName: varchar('drug_name', { length: 255 }).notNull(),
    genericName: varchar('generic_name', { length: 255 }),
    brandName: varchar('brand_name', { length: 255 }),
    strength: varchar({ length: 100 }),
    dosageForm: varchar('dosage_form', { length: 100 }),
    routeOfAdministration: varchar('route_of_administration', { length: 100 }),
    manufacturer: varchar({ length: 255 }),
    ndc: varchar({ length: 20 }),
    rxcui: varchar({ length: 20 }),
    drugType: enumDrugType('drug_type').default('GENERIC'),
    isControlled: boolean('is_controlled').default(false),
    controlledSubstanceSchedule: varchar('controlled_substance_schedule', {
      length: 10
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    description: text(),
    sideEffects: text('side_effects'),
    contraindications: text(),
    interactions: text(),
    dosageInstructions: text('dosage_instructions'),
    warnings: text(),
    activeIngredients: json('active_ingredients'),
    inactiveIngredients: json('inactive_ingredients')
  }
);

// Prescriptions table
export const prescriptions = pgTable(
  'prescriptions',
  {
    prescriptionId: serial('prescription_id').primaryKey().notNull(),
    patientId: integer('patient_id').notNull(),
    providerId: integer('provider_id').notNull(),
    drugId: integer('drug_id').notNull(),
    pharmacyId: integer('pharmacy_id'),
    orderId: integer('order_id'),
    prescriptionNumber: varchar('prescription_number', { length: 50 }),
    quantity: integer(),
    daysSupply: integer('days_supply'),
    refillsRemaining: integer('refills_remaining'),
    totalRefills: integer('total_refills'),
    dosageInstructions: text('dosage_instructions'),
    status: enumPrescriptionStatus().default('PENDING'),
    prescribedAt: timestamp('prescribed_at', {
      withTimezone: true,
      mode: 'string'
    }),
    filledAt: timestamp('filled_at', { withTimezone: true, mode: 'string' }),
    deliveredAt: timestamp('delivered_at', {
      withTimezone: true,
      mode: 'string'
    }),
    expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    dosespotPrescriptionId: varchar('dosespot_prescription_id', {
      length: 200
    }),
    externalPrescriptionId: varchar('external_prescription_id', {
      length: 200
    }),
    notes: text(),
    isElectronic: boolean('is_electronic').default(true),
    isSent: boolean('is_sent').default(false),
    sentAt: timestamp('sent_at', { withTimezone: true, mode: 'string' }),
    cancelledAt: timestamp('cancelled_at', {
      withTimezone: true,
      mode: 'string'
    }),
    cancellationReason: text('cancellation_reason'),
    priorAuthorizationRequired: boolean('prior_authorization_required').default(
      false
    ),
    priorAuthorizationNumber: varchar('prior_authorization_number', {
      length: 100
    }),
    cost: numeric({ precision: 10, scale: 2 }),
    copay: numeric({ precision: 10, scale: 2 }),
    insuranceCoverage: numeric('insurance_coverage', {
      precision: 10,
      scale: 2
    })
  },
  (table) => [
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'prescriptions_patient_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'prescriptions_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.drugId],
      foreignColumns: [drugs.drugId],
      name: 'prescriptions_drug_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacy.pharmacyId],
      name: 'prescriptions_pharmacy_id_fkey'
    }),
    unique('prescriptions_prescription_number_key').on(table.prescriptionNumber)
  ]
);

// Prescription Refills table
export const prescriptionRefills = pgTable(
  'prescription_refills',
  {
    refillId: serial('refill_id').primaryKey().notNull(),
    prescriptionId: integer('prescription_id').notNull(),
    refillNumber: integer('refill_number').notNull(),
    quantity: integer(),
    filledAt: timestamp('filled_at', { withTimezone: true, mode: 'string' }),
    deliveredAt: timestamp('delivered_at', {
      withTimezone: true,
      mode: 'string'
    }),
    pharmacyId: integer('pharmacy_id'),
    cost: numeric({ precision: 10, scale: 2 }),
    copay: numeric({ precision: 10, scale: 2 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    notes: text()
  },
  (table) => [
    foreignKey({
      columns: [table.prescriptionId],
      foreignColumns: [prescriptions.prescriptionId],
      name: 'prescription_refills_prescription_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacy.pharmacyId],
      name: 'prescription_refills_pharmacy_id_fkey'
    })
  ]
);

// Drug Interactions table
export const drugInteractions = pgTable(
  'drug_interactions',
  {
    interactionId: serial('interaction_id').primaryKey().notNull(),
    drug1Id: integer('drug1_id').notNull(),
    drug2Id: integer('drug2_id').notNull(),
    severityLevel: varchar('severity_level', { length: 20 }),
    description: text(),
    clinicalEffects: text('clinical_effects'),
    mechanism: text(),
    management: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.drug1Id],
      foreignColumns: [drugs.drugId],
      name: 'drug_interactions_drug1_id_fkey'
    }),
    foreignKey({
      columns: [table.drug2Id],
      foreignColumns: [drugs.drugId],
      name: 'drug_interactions_drug2_id_fkey'
    })
  ]
);
