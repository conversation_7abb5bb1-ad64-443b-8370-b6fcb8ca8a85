import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  unique,
  uuid,
  json,
  pgEnum
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm/relations';

// Enums related to users
export const enumUsersGender = pgEnum('enum_users_gender', [
  'male',
  'female',
  'others',
  'transgender-female',
  'transgender-male'
]);

export const enumUsersRole = pgEnum('enum_users_role', [
  'USER',
  'BUSER',
  'AUSER',
  'medical_assistant',
  'viewer',
  'support_user',
  'pharmacist',
  'PHARMACY',
  'GROUP_ADMIN',
  'SUPPORT_ADMIN',
  'DEVELOPER'
]);

export const enumUsersStatus = pgEnum('enum_users_status', [
  'AVAILABLE',
  'BUSY',
  'AWAY',
  'OFFLINE',
  'ACTIVATION_PENDING',
  'ONBOARDING_PENDING',
  'PROFILE_INCOMPLETE'
]);

export const enumUsersSubRole = pgEnum('enum_users_sub_role', [
  'USER',
  'DIETICIAN_NUTRITION',
  'MENTAL_HEALTH',
  'WEIGHT_LOSS_MANAGEMENT',
  'DIABETES_PREVENTION'
]);

// Users table
export const users = pgTable(
  'users',
  {
    userId: serial('user_id').primaryKey().notNull(),
    userGuid: varchar('user_guid', { length: 255 }),
    password: text(),
    role: enumUsersRole().default('USER'),
    status: enumUsersStatus().default('OFFLINE'),
    installType: varchar('install_type', { length: 255 }),
    firstName: text('first_name'),
    lastName: text('last_name'),
    zipCode: varchar('zip_code', { length: 255 }),
    email: text().notNull(),
    phone: text(),
    otp: text(),
    dob: text(),
    apptLength: integer('appt_length'),
    apptStartTime: varchar('appt_start_time', { length: 255 }),
    apptEndTime: varchar('appt_end_time', { length: 255 }),
    secureMessage: boolean('secure_message').default(false),
    connectionRequests: boolean('connection_requests').default(false),
    vitalsCcdEnabled: boolean('vitals_ccd_enabled').default(false),
    apptRequests: boolean('appt_requests').default(false),
    trialValidity: timestamp('trial_validity', {
      withTimezone: true,
      mode: 'string'
    }),
    isCcCaptured: boolean('is_cc_captured'),
    gender: enumUsersGender().default('male'),
    deleted: boolean().default(false),
    emailVerified: boolean('email_verified').default(false),
    userAvatar: text('user_avatar'),
    idCard: text('id_card'),
    history: text(),
    questionnaire: text(),
    tokenValidity: integer('token_validity'),
    emailVerificationDetails: text('email_verification_details'),
    lastActive: timestamp('last_active', {
      withTimezone: true,
      mode: 'string'
    }),
    locked: timestamp({ withTimezone: true, mode: 'string' }),
    registrationKey: text('registration_key'),
    isRpmEnabled: boolean('is_rpm_enabled').default(false),
    isNotifyOnCapture: boolean('is_notify_on_capture').default(false),
    vitalsThresholds: text('vitals_thresholds'),
    vitalsCron: text('vitals_cron'),
    appDetails: text('app_details'),
    historyUpdatedAt: timestamp('history_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    questionnaireUpdatedAt: timestamp('questionnaire_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    debug: boolean().default(false),
    invitationCodeValidity: integer('invitation_code_validity'),
    cronExpression: text('cron_expression'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    mirthCcdEnabled: boolean('mirth_ccd_enabled').default(false),
    ccPaymentAccepted: boolean('cc_payment_accepted').default(false),
    recordingEnabled: boolean('recording_enabled').default(true),
    transcriptionEnabled: boolean('transcription_enabled').default(true),
    ordersEnabled: boolean('orders_enabled').default(true),
    showHealthSummaries: boolean('show_health_summaries'),
    healthgorillaId: varchar('healthgorilla_id', { length: 255 }),
    releaseMedical: boolean('release_medical'),
    telehealthServiceCost: text('telehealth_service_cost').default('0'),
    subRole: enumUsersSubRole('sub_role').default('USER'),
    myInviteCode: varchar('my_invite_code', { length: 15 }).default(sql`NULL`),
    referredByInviteCode: varchar('referred_by_invite_code', {
      length: 15
    }).default(sql`NULL`),
    referredByUserId: integer('referred_by_user_id'),
    email2: text('email_2'),
    tennantId: integer('tennant_id'),
    secondaryPhone: text('secondary_phone'),
    isTennantOwner: boolean('is_tennant_owner').default(false).notNull(),
    sendEmailCampaign: boolean('send_email_campaign').default(false).notNull(),
    appTimezone: varchar('app_timezone', { length: 255 }).default('{}'),
    rxPersonId: varchar({ length: 50 }),
    rxStatus: boolean().default(false),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotApiResponse: varchar('dosespot_api_response', { length: 2000 }),
    idCardFile: text('id_card_file'),
    pharmacyAccess: varchar('pharmacy_access', { length: 500 }),
    parentId: integer('parent_id'),
    dependentAccountRelation: varchar('dependent_account_relation', {
      length: 255
    }),
    trackingCode: varchar('tracking_code', { length: 255 }),
    userPrivateKey: text('user_private_key')
  },
  (table) => [
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.userId],
      name: 'users_parent_id_fkey'
    }),
    unique('users_my_invite_code_key').on(table.myInviteCode)
  ]
);

// Auth Provider table
export const authProvider = pgTable(
  'auth_provider',
  {
    authId: serial('auth_id').notNull(),
    userId: integer('user_id'),
    jsonTokenId: text('json_token_id'),
    refreshToken: text('refresh_token'),
    iat: timestamp({ withTimezone: true, mode: 'string' }),
    isJtiValid: boolean('is_jti_valid').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'auth_provider_user_id_fkey'
    })
  ]
);

// User Details table
export const userDetails = pgTable(
  'user_details',
  {
    userDetailId: serial('user_detail_id').primaryKey().notNull(),
    userId: integer('user_id'),
    data: text(),
    signature: varchar({ length: 255 }),
    specialty: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    address: text(),
    deleted: boolean().default(false),
    bio: text(),
    language: json().default([])
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_details_user_id_fkey'
    })
  ]
);

// User Identities table
export const userIdentities = pgTable(
  'user_identities',
  {
    userIdentityId: serial('user_identity_id').primaryKey().notNull(),
    userId: integer('user_id'),
    identifier: text(),
    type: varchar({ length: 255 }),
    installType: varchar('install_type', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_identities_user_id_fkey'
    })
  ]
);

// Encryption Keys table
export const encryptionKeys = pgTable(
  'encryption_keys',
  {
    encryptionKeyId: serial('encryption_key_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    deviceId: integer('device_id').default(1).notNull(),
    registrationId: integer('registration_id').notNull(),
    identityKey: text('identity_key'),
    signedPreKey: text('signed_pre_key'),
    preKey: text('pre_key'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'encryption_keys_user_id_fkey'
    })
  ]
);

// Login Requests table
export const loginRequests = pgTable(
  'login_requests',
  {
    loginRequestId: serial('login_request_id').primaryKey().notNull(),
    userId: integer('user_id'),
    badRequest: boolean('bad_request').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'login_requests_user_id_fkey'
    })
  ]
);

// Invitations table
export const invitations = pgTable(
  'invitations',
  {
    invitationId: serial('invitation_id').primaryKey().notNull(),
    invitorId: integer('invitor_id').notNull(),
    installType: varchar('install_type', { length: 255 }),
    email: varchar({ length: 255 }),
    phone: varchar({ length: 255 }),
    accepted: boolean().default(false),
    addPracticeGroupDoctors: boolean('add_practice_group_doctors').default(
      false
    ),
    code: varchar({ length: 255 }),
    expiry: timestamp({ withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.invitorId],
      foreignColumns: [users.userId],
      name: 'invitations_invitor_id_fkey'
    })
  ]
);

// Mobile Number OTP Validator table
export const mobileNumberOtpValidator = pgTable('mobile_number_otp_validator', {
  id: uuid().primaryKey().notNull(),
  phone: varchar({ length: 20 }).notNull(),
  otp: varchar({ length: 1000 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

// User External ID Mapping table
export const userExternalIdMapping = pgTable(
  'user_external_id_mapping',
  {
    userExternalIdMappingId: serial('user_external_id_mapping_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id').notNull(),
    externalId: varchar('external_id', { length: 255 }).notNull(),
    externalIdSource: varchar('external_id_source', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_external_id_mapping_user_id_fkey'
    })
  ]
);
