// This file is deprecated and will be removed after refactoring
// Please use the modular schema files in src/db/schemas/ instead

import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  numeric,
  unique,
  type AnyPgColumn,
  uuid,
  doublePrecision,
  json,
  index,
  jsonb,
  primaryKey,
  pgView,
  bigint,
  pgMaterializedView,
  pgEnum
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

export const enumConsultOrderFilesType = pgEnum(
  'enum_consult_order_files_type',
  ['audio', 'video', 'file', 'image']
);
export const enumFollowUpReminderStatus = pgEnum(
  'enum_follow_up_reminder_status',
  ['pending', 'started', 'failed', 'sent', 'cancel', 'cancelled_by_admin']
);
export const enumJobsStatus = pgEnum('enum_jobs_status', [
  'pending',
  'started',
  'failed',
  'sending_fax',
  'completed'
]);
export const enumLifefileConfigurationShippingServices = pgEnum(
  'enum_lifefile_configuration_shipping_services',
  ['7780', '9']
);
export const enumNotificationReminderNotificationForType = pgEnum(
  'enum_notification_reminder_notification_for_type',
  ['schedule_pending', 'intake_questionnaire', 'follow_up']
);
export const enumNotificationReminderNotificationStatus = pgEnum(
  'enum_notification_reminder_notification_status',
  ['pending', 'sent', 'failed']
);
export const enumOnehealthLabOrdersPaymentStatus = pgEnum(
  'enum_onehealth_lab_orders_payment_status',
  ['PENDING', 'FAILED', 'PAID']
);
export const enumOnehealthLabOrdersStatus = pgEnum(
  'enum_onehealth_lab_orders_status',
  ['PENDING', 'PAYMENT_PENDING', 'COMPLETE_COLLECTION', 'COMPLETED']
);
export const enumOrdersCategory = pgEnum('enum_orders_category', [
  'CCM',
  'RPM',
  'BHI'
]);
export const enumOrdersStatus = pgEnum('enum_orders_status', [
  'STARTED',
  'INPROGRESS',
  'ENDED',
  'NOANSWER',
  'REJECTED',
  'QUEUED',
  'RINGING',
  'CANCELLED',
  'COMPLETED',
  'BUSY',
  'FAILED'
]);
export const enumOrdersType = pgEnum('enum_orders_type', [
  'audio',
  'video',
  'one-way',
  'online',
  'voip'
]);
export const enumPaymentDetailsPaymentGateway = pgEnum(
  'enum_payment_details_payment_gateway',
  ['paypal', 'stripe', 'recurly', 'authorize_net', 'no_payment', 'nmi']
);
export const enumPrescriptionPreferencePreference = pgEnum(
  'enum_prescription_preference_preference',
  ['fax', 'life_file', 'doespot', 'wellsync-pharmacy-hub']
);
export const enumPrescriptionTransferMedicationsStatus = pgEnum(
  'enum_prescription_transfer_medications_status',
  ['pending', 'transferred']
);
export const enumPromoCodesCodeType = pgEnum('enum_promo_codes_code_type', [
  'PROMO CODE',
  'INVITE PROMO CODE',
  'MD',
  'LIFESTYLE',
  'LAB',
  'MD/LIFESTYLE/LAB'
]);
export const enumPromoCodesDiscountType = pgEnum(
  'enum_promo_codes_discount_type',
  ['PERCENTAGE', 'DOLLAR']
);
export const enumPromoCodesUsageType = pgEnum('enum_promo_codes_usage_type', [
  'SINGLE',
  'MULTIPLE'
]);
export const enumReferralTrackingShippingPartner = pgEnum(
  'enum_referral_tracking_shipping_partner',
  ['UPS', 'USPS', 'FedEx']
);
export const enumReferralsStatus = pgEnum('enum_referrals_status', [
  'issued',
  'canceled',
  'pdf_pending'
]);
export const enumRequestsStatus = pgEnum('enum_requests_status', [
  'open',
  'accepted',
  'rejected'
]);
export const enumServicePaymentMappingPaymentType = pgEnum(
  'enum_service_payment_mapping_payment_type',
  ['subscription', 'one-time', 'one-time-insurance', 'no_payment']
);
export const enumStripeUserDetailsCcStatus = pgEnum(
  'enum_stripe_user_details_cc_status',
  ['captured', 'not_captured', 'payment_error', 'capture_immediate']
);
export const enumStripeUserDetailsOauthStatus = pgEnum(
  'enum_stripe_user_details_oauth_status',
  ['connected', 'not_connected', 'payouts_disabled']
);
export const enumStripeUserPaymentDetailsPaymentStatus = pgEnum(
  'enum_stripe_user_payment_details_payment_status',
  ['success', 'failed', 'action_required']
);
export const enumSubscriptionPlansBillingCycle = pgEnum(
  'enum_subscription_plans_billing_cycle',
  ['monthly', 'yearly', 'weekly']
);
export const enumTelehealthServiceOrderClaimType = pgEnum(
  'enum_telehealth_service_order_claim_type',
  ['AUTO', 'WORK']
);
export const enumTelehealthServiceOrderServiceType = pgEnum(
  'enum_telehealth_service_order_service_type',
  ['SYNC', 'ASYNC']
);
export const enumTelehealthServiceOrderStatus = pgEnum(
  'enum_telehealth_service_order_status',
  [
    'pending',
    'accept',
    'completed',
    'errored',
    'cancelled',
    'patient_verification_pending',
    'archive',
    'cancelled_by_provider',
    'LabRequested',
    'LabReceived',
    'schedule_pending',
    'lab_approval_pending',
    'lab_results_approved',
    'lab_results_denied',
    'clinical_denial',
    'cancelled_by_patient',
    'now_show',
    'no_show',
    'payment_pending',
    'pharmacy_pending',
    'questionnaire_pending',
    'user_consent_pending'
  ]
);
export const enumTelehealthServiceOrderVisitType = pgEnum(
  'enum_telehealth_service_order_visit_type',
  ['IN_PERSON', 'ONLINE']
);
export const enumTelehealthServiceQuestionsQuestionFor = pgEnum(
  'enum_telehealth_service_questions_question_for',
  ['male', 'female', 'both', 'provider']
);
export const enumTelehealthServiceQuestionsQuestionType = pgEnum(
  'enum_telehealth_service_questions_question_type',
  [
    'YesNo',
    'Text',
    'Selection',
    'MultipleSelection',
    'Date',
    'DateTime',
    'TextArea',
    'Height',
    'Weight',
    'Bmi',
    'FileUpload',
    'HeightWeightBmi'
  ]
);
export const enumTelehealthServiceStateMappingServiceType = pgEnum(
  'enum_telehealth_service_state_mapping_service_type',
  ['SYNC', 'ASYNC']
);
export const enumTelehealthServicesServiceMode = pgEnum(
  'enum_telehealth_services_service_mode',
  ['SYNC', 'ASYNC', 'BOTH_SYNC_ASYNC']
);
export const enumTelehealthServicesServiceType = pgEnum(
  'enum_telehealth_services_service_type',
  [
    'BUSER',
    'AUSER',
    'medical_assistant',
    'pharmacist',
    'DIETICIAN_NUTRITION',
    'MENTAL_HEALTH',
    'WEIGHT_LOSS_MANAGEMENT',
    'DIABETES_PREVENTION'
  ]
);
export const enumTennantMasterPreferredPaymentGateway = pgEnum(
  'enum_tennant_master_preferred_payment_gateway',
  ['PAYPAL', 'STRIPE']
);
export const enumTransactionsPaymentMethodType = pgEnum(
  'enum_transactions_payment_method_type',
  ['STRIPE', 'BRAINTREE', 'PAYPAL', 'RECURLY', 'AUTHORISED_NET', 'NMI']
);
export const enumTransactionsPaymentStatus = pgEnum(
  'enum_transactions_payment_status',
  ['pending', 'completed', 'errored', 'cancelled', 'RECURLY', 'AUTHORISED_NET']
);
export const enumTransactionsRefundPaymentStatus = pgEnum(
  'enum_transactions_refund_payment_status',
  ['succeeded', 'failed', 'pending', 'n/a']
);
export const enumTransactionsStatus = pgEnum('enum_transactions_status', [
  'succeeded',
  'failed',
  'pending'
]);
export const enumTransactionsTransactionStatus = pgEnum(
  'enum_transactions_transaction_status',
  ['initiated', 'completed']
);
export const enumUserDietMealType = pgEnum('enum_user_diet_meal_type', [
  'breakfast',
  'lunch',
  'dinner',
  'snacks'
]);
export const enumUserFileRepoDetailsUploadType = pgEnum(
  'enum_user_file_repo_details_upload_type',
  ['FAX', 'SFTP']
);
export const enumUserFilesType = pgEnum('enum_user_files_type', [
  'audio',
  'video',
  'file',
  'image'
]);
export const enumUserVitalsMode = pgEnum('enum_user_vitals_mode', [
  'automated',
  'doctor',
  'patient'
]);
export const enumUsersGender = pgEnum('enum_users_gender', [
  'male',
  'female',
  'others',
  'transgender-female',
  'transgender-male'
]);
export const enumUsersRole = pgEnum('enum_users_role', [
  'USER',
  'BUSER',
  'AUSER',
  'medical_assistant',
  'viewer',
  'support_user',
  'pharmacist',
  'PHARMACY',
  'GROUP_ADMIN',
  'SUPPORT_ADMIN',
  'DEVELOPER'
]);
export const enumUsersStatus = pgEnum('enum_users_status', [
  'AVAILABLE',
  'BUSY',
  'AWAY',
  'OFFLINE',
  'ACTIVATION_PENDING',
  'ONBOARDING_PENDING',
  'PROFILE_INCOMPLETE'
]);
export const enumUsersSubRole = pgEnum('enum_users_sub_role', [
  'USER',
  'DIETICIAN_NUTRITION',
  'MENTAL_HEALTH',
  'WEIGHT_LOSS_MANAGEMENT',
  'DIABETES_PREVENTION'
]);
export const enumVisitSummaryUploadStatusUploadStatus = pgEnum(
  'enum_visit_summary_upload_status_upload_status',
  ['SUCCESS', 'FAILED']
);
export const enumVisitSummaryUploadStatusUploadType = pgEnum(
  'enum_visit_summary_upload_status_upload_type',
  ['FAX', 'SFTP']
);
export const enumVitalsSummaryUploadStatusUploadStatus = pgEnum(
  'enum_vitals_summary_upload_status_upload_status',
  ['SUCCESS', 'FAILED']
);
export const enumVitalsSummaryUploadStatusUploadType = pgEnum(
  'enum_vitals_summary_upload_status_upload_type',
  ['FAX', 'SFTP']
);
export const enumWebhooksLogActionType = pgEnum(
  'enum_webhooks_log_action_type',
  ['webhook', 'prescription_api']
);
export const enumWebhooksLogStatus = pgEnum('enum_webhooks_log_status', [
  'pending',
  'success',
  'failed'
]);

export const sequelizeMeta = pgTable('SequelizeMeta', {
  name: varchar({ length: 255 }).primaryKey().notNull()
});

export const authProvider = pgTable(
  'auth_provider',
  {
    authId: serial('auth_id').notNull(),
    userId: integer('user_id'),
    jsonTokenId: text('json_token_id'),
    refreshToken: text('refresh_token'),
    iat: timestamp({ withTimezone: true, mode: 'string' }),
    isJtiValid: boolean('is_jti_valid').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'auth_provider_user_id_fkey'
    })
  ]
);

export const billing = pgTable('billing', {
  planId: serial('plan_id').notNull(),
  planName: text('plan_name'),
  planDescription: text('plan_description'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const contactUs = pgTable('contact_us', {
  contactUsId: serial('contact_us_id').primaryKey().notNull(),
  userId: integer('user_id').notNull(),
  dateRequested: timestamp('date_requested', {
    withTimezone: true,
    mode: 'string'
  }),
  title: varchar({ length: 1000 }).default('').notNull(),
  question: varchar({ length: 1000 }).default('').notNull(),
  queryText: varchar({ length: 4000 }).default('').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const consultNotes = pgTable(
  'consult_notes',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id').notNull(),
    icdCode: text('icd_code').default(''),
    cptCode: text('cpt_code').default(''),
    subjective: text().default(''),
    objective: text().default(''),
    assessment: text().default(''),
    plan: text().default(''),
    ermId: varchar('erm_id', { length: 150 }).default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    intervention: text(),
    outcome: text(),
    goal: text(),
    orderGuid: varchar('order_guid', { length: 50 }),
    generalNote: varchar('general_note', { length: 2000 }).default(sql`NULL`)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'consult_notes_order_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'consult_notes_order_guid_fkey'
    })
  ]
);

export const affiliatePharmacy = pgTable('affiliate_pharmacy', {
  id: serial().primaryKey().notNull(),
  pharmacyName: varchar('pharmacy_name', { length: 250 }).default(''),
  streetOne: varchar('street_one', { length: 255 }).default(''),
  streetTwo: varchar('street_two', { length: 255 }).default(''),
  city: varchar({ length: 75 }).default(''),
  state: varchar({ length: 50 }).default(''),
  zipCode: varchar('zip_code', { length: 20 }).default(''),
  country: varchar({ length: 50 }).default(''),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  email: varchar({ length: 200 }),
  faxNumber: varchar('fax_number', { length: 20 }),
  phoneNumber: varchar('phone_number', { length: 20 })
});

export const educationalVideos = pgTable(
  'educational_videos',
  {
    videoId: serial('video_id').primaryKey().notNull(),
    sessionId: text('session_id'),
    archiveId: text('archive_id'),
    title: text().notNull(),
    description: text(),
    url: text(),
    createdBy: integer('created_by'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    audioStreamScreenshot: text('audio_stream_screenshot')
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'educational_videos_created_by_fkey'
    })
  ]
);

export const encountersValues = pgTable(
  'encounters_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'encounters_values_summary_id_fkey'
    })
  ]
);

export const requests = pgTable(
  'requests',
  {
    requestId: serial('request_id').primaryKey().notNull(),
    requestorId: integer('requestor_id').notNull(),
    requesteeId: integer('requestee_id').notNull(),
    objectId: integer('object_id').notNull(),
    status: enumRequestsStatus().default('open'),
    message: varchar({ length: 255 }),
    detail: text(),
    patientHistory: text('patient_history'),
    requestorReadStatus: boolean('requestor_read_status').default(false),
    requesteeReadStatus: boolean('requestee_read_status').default(false),
    entityId: integer('entity_id'),
    addPracticeGroupDoctors: boolean('add_practice_group_doctors').default(
      false
    ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    releaseMedical: boolean('release_medical').default(false),
    rescheduled: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.objectId],
      foreignColumns: [requestObjects.objectId],
      name: 'requests_object_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'requests_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.requesteeId],
      foreignColumns: [users.userId],
      name: 'requests_requestee_id_fkey'
    }),
    foreignKey({
      columns: [table.requestorId],
      foreignColumns: [users.userId],
      name: 'requests_requestor_id_fkey'
    })
  ]
);

export const encryptionKeys = pgTable(
  'encryption_keys',
  {
    encryptionKeyId: serial('encryption_key_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    deviceId: integer('device_id').default(1).notNull(),
    registrationId: integer('registration_id').notNull(),
    identityKey: text('identity_key'),
    signedPreKey: text('signed_pre_key'),
    preKey: text('pre_key'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'encryption_keys_user_id_fkey'
    })
  ]
);

export const healthSummariesSchedule = pgTable(
  'health_summaries_schedule',
  {
    scheduleId: serial('schedule_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    nextRunDate: timestamp('next_run_date', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'health_summaries_schedule_user_id_fkey'
    })
  ]
);

export const precannedMessages = pgTable('precanned_messages', {
  messageId: serial('message_id').primaryKey().notNull(),
  message: text().notNull(),
  type: varchar({ length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const feed = pgTable(
  'feed',
  {
    feedId: serial('feed_id').primaryKey().notNull(),
    description: text(),
    address: text(),
    imageUrl: text('image_url'),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    metadata: text(),
    userId: integer('user_id'),
    createdBy: integer('created_by'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'feed_created_by_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'feed_user_id_fkey'
    })
  ]
);

export const forms = pgTable('forms', {
  formId: serial('form_id').primaryKey().notNull(),
  name: text().notNull(),
  url: text(),
  data: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const promoCodes = pgTable(
  'promo_codes',
  {
    codeId: serial('code_id').primaryKey().notNull(),
    codeType: enumPromoCodesCodeType('code_type').notNull(),
    serviceId: integer('service_id'),
    labId: integer('lab_id'),
    discountType: enumPromoCodesDiscountType('discount_type'),
    discountValues: text('discount_values'),
    promoCode: text('promo_code'),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    usageType: enumPromoCodesUsageType('usage_type'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    isDeleted: boolean('is_deleted').default(false),
    createdBy: integer('created_by'),
    updatedBy: integer('updated_by'),
    deletedBy: integer('deleted_by')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'promo_codes_service_id_fkey'
    })
  ]
);

export const drugs = pgTable(
  'drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    categoryId: integer('category_id'),
    drugFullName: varchar('drug_full_name', { length: 200 }).notNull(),
    tier: varchar({ length: 200 }).notNull(),
    price: varchar({ length: 200 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    quantity: varchar({ length: 200 })
  },
  (table) => [
    foreignKey({
      columns: [table.categoryId],
      foreignColumns: [drugsCategory.categoryId],
      name: 'drugs_category_id_fkey'
    })
  ]
);

export const invitations = pgTable(
  'invitations',
  {
    invitationId: serial('invitation_id').primaryKey().notNull(),
    invitorId: integer('invitor_id').notNull(),
    installType: varchar('install_type', { length: 255 }),
    email: varchar({ length: 255 }),
    phone: varchar({ length: 255 }),
    accepted: boolean().default(false),
    addPracticeGroupDoctors: boolean('add_practice_group_doctors').default(
      false
    ),
    code: varchar({ length: 255 }),
    expiry: timestamp({ withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.invitorId],
      foreignColumns: [users.userId],
      name: 'invitations_invitor_id_fkey'
    })
  ]
);

export const loginRequests = pgTable(
  'login_requests',
  {
    loginRequestId: serial('login_request_id').primaryKey().notNull(),
    userId: integer('user_id'),
    badRequest: boolean('bad_request').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'login_requests_user_id_fkey'
    })
  ]
);

export const favouriteDrugs = pgTable(
  'favourite_drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    drugName: varchar('drug_name', { length: 255 }).notNull(),
    brand: varchar({ length: 255 }).notNull(),
    form: varchar({ length: 255 }).notNull(),
    dosage: varchar({ length: 255 }).notNull(),
    quantity: varchar({ length: 255 }).notNull(),
    refillQuantity: varchar('refill_quantity', { length: 255 }).notNull(),
    favourite: boolean().default(true).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    directionQuantity: varchar('direction_quantity', { length: 255 }),
    directionOne: varchar('direction_one', { length: 255 }),
    directionTwo: varchar('direction_two', { length: 255 }),
    tennantId: varchar('tennant_id', { length: 255 }),
    pharmacyName: varchar('pharmacy_name', { length: 255 }),
    quantityUnit: varchar('quantity_unit', { length: 100 }),
    erxProductId: varchar('erx_product_id', { length: 200 }),
    displayOrder: integer('display_order'),
    comments: text()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'favourite_drugs_user_id_fkey'
    })
  ]
);

export const micromerchantUsers = pgTable('micromerchant_users', {
  mmUserId: serial('mm_user_id').primaryKey().notNull(),
  firstName: text('first_name'),
  lastName: text('last_name'),
  zipCode: varchar('zip_code', { length: 255 }),
  email: text().notNull(),
  phone: varchar({ length: 255 }),
  gender: varchar({ length: 255 }),
  data: text(),
  dob: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const procedureCodes = pgTable('procedure_codes', {
  procedureCodeId: serial('procedure_code_id').primaryKey().notNull(),
  code: varchar({ length: 255 }),
  description: varchar({ length: 500 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean(),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
});

export const immunizationsValues = pgTable(
  'immunizations_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'immunizations_values_summary_id_fkey'
    })
  ]
);

export const medicationsValues = pgTable(
  'medications_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'medications_values_summary_id_fkey'
    })
  ]
);

export const mmsPatients = pgTable(
  'mms_patients',
  {
    patientId: serial('patient_id').primaryKey().notNull(),
    patientNo: varchar('patient_no', { length: 255 }).notNull(),
    pharmacyId: integer('pharmacy_id'),
    userId: integer('user_id'),
    firstName: varchar('first_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }),
    gender: varchar({ length: 255 }),
    phone: varchar({ length: 255 }),
    email: varchar({ length: 255 }),
    active: varchar({ length: 255 }),
    address: text(),
    notes: text(),
    paymentPreference: text('payment_preference'),
    messagingSettings: text('messaging_settings'),
    diagnostics: text(),
    allergies: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    insurances: text(),
    source: varchar({ length: 255 }),
    dob: text(),
    isSmoker: boolean('is_smoker').default(false),
    maritalStatus: varchar('marital_status', { length: 20 }),
    weight: numeric({ precision: 10, scale: 3 }),
    isPregnant: boolean('is_pregnant').default(false),
    height: numeric({ precision: 10, scale: 3 }),
    medicalRecordNumber: varchar('medical_record_number', { length: 50 }),
    speciesType: varchar('species_type', { length: 15 }),
    deaRestrictionCode: varchar('dea_restriction_code', { length: 50 }),
    familyEmail: varchar('family_email', { length: 255 }),
    patientRemark: varchar('patient_remark', { length: 255 }),
    patientShortRemark: varchar('patient_short_remark', { length: 255 }),
    familyRemark: varchar('family_remark', { length: 255 }),
    race: varchar({ length: 30 }),
    workPhone: varchar('work_phone', { length: 15 }),
    mobile: varchar({ length: 15 }),
    chartNo: varchar('chart_no', { length: 30 }),
    language: varchar({ length: 30 }),
    ezCap: boolean('ez_cap').default(false),
    discountCode: varchar('discount_code', { length: 30 }),
    shortSode: varchar('short_sode', { length: 30 }),
    priceCodeBrand: varchar('price_code_brand', { length: 30 }),
    priceCodeGeneric: varchar('price_code_generic', { length: 30 }),
    chargeAccount: varchar('charge_account', { length: 30 }),
    printDrugCounselling: varchar('print_drug_counselling', { length: 30 }),
    category: varchar({ length: 30 }),
    preferredDeliveryMethod: varchar('preferred_delivery_method', {
      length: 50
    }),
    driverLicenseNumber: varchar('driver_license_number', { length: 50 }),
    hippaSignature: varchar('hippa_signature', { length: 50 }),
    driverLicenseExpiry: varchar('driver_license_expiry', { length: 50 })
  },
  (table) => [
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'mms_patients_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'mms_patients_user_id_fkey'
    })
  ]
);

export const mmsRequestPayload = pgTable('mms_request_payload', {
  id: integer().primaryKey().notNull(),
  rxno: varchar({ length: 50 }).notNull(),
  pharmacySystem: varchar('pharmacy_system', { length: 50 }).notNull(),
  pharmacyToken: varchar('pharmacy_token', { length: 50 }),
  payload: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const mmsPrescriptions = pgTable(
  'mms_prescriptions',
  {
    prescriptionId: serial('prescription_id').primaryKey().notNull(),
    patientId: integer('patient_id').notNull(),
    rxNo: varchar('rx_no', { length: 255 }),
    authRefills: varchar('auth_refills', { length: 255 }),
    drugInfo: text('drug_info'),
    qtyOrdered: numeric('qty_ordered'),
    dateOrdered: varchar('date_ordered', { length: 255 }),
    dateExpires: varchar('date_expires', { length: 255 }),
    discontinued: varchar({ length: 255 }),
    sig: varchar({ length: 255 }),
    prescriber: text(),
    diagnostics: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    auth: varchar({ length: 50 }),
    sourcePms: varchar('source_pms', { length: 50 }),
    refillNo: varchar('refill_no', { length: 50 }),
    pharmacyId: integer('pharmacy_id'),
    dateFilled: varchar('date_filled', { length: 50 }),
    discontinueReason: varchar('discontinue_reason', { length: 50 }),
    dateDiscontinued: varchar('date_discontinued', { length: 50 }),
    status: varchar({ length: 50 }),
    daysSupplied: varchar('days_supplied', { length: 50 }),
    rxSerialNo: varchar('rx_serial_no', { length: 50 }),
    billType: varchar('bill_type', { length: 50 }),
    billAs: varchar('bill_as', { length: 50 }),
    qtyDispensed: varchar('qty_dispensed', { length: 50 }),
    deliveryMethod: varchar('delivery_method', { length: 50 }),
    holdRx: varchar('hold_rx', { length: 50 }),
    rph: varchar({ length: 50 }),
    datePicked: varchar('date_picked', { length: 30 }),
    awp: varchar({ length: 50 }),
    timePicked: varchar('time_picked', { length: 30 }),
    pickedUp: varchar('picked_up', { length: 50 }),
    prescriptionNotes: varchar('prescription_notes', { length: 255 }),
    deliveryDate: varchar('delivery_date', { length: 30 }),
    trackingUrl: varchar('tracking_url', { length: 50 }),
    shippingService: varchar('shipping_service', { length: 50 }),
    sourceScriptId: varchar('source_script_id', { length: 50 }),
    flag340B: varchar({ length: 50 }),
    dawCode: varchar('daw_code', { length: 50 }),
    dispenseAsWritten: varchar('dispense_as_written', { length: 50 }),
    sourceScriptMessage: varchar('source_script_message', { length: 50 }),
    costPrice: varchar('cost_price', { length: 50 }),
    rxAmount: varchar('rx_amount', { length: 50 }),
    dispFee: varchar('disp_fee', { length: 50 }),
    dosageFields: text('dosage_fields'),
    patientCopay: varchar('patient_copay', { length: 50 }),
    billed: varchar({ length: 50 }),
    primaryInsurance: text('primary_insurance'),
    fillListIndicator: varchar('fill_list_indicator', { length: 50 }),
    submissionClarCode: varchar('submission_clar_code', { length: 50 }),
    horizonGraveyardCode: varchar('horizon_graveyard_code', { length: 50 }),
    roomNumber: varchar('room_number', { length: 10 }),
    nursingHomeId: varchar('nursing_home_id', { length: 30 }),
    locationCode: varchar('location_code', { length: 20 }),
    facilityCode: varchar('facility_code', { length: 20 }),
    nursingHome: varchar('nursing_home', { length: 50 }),
    wingCode1: varchar('wing_code1', { length: 50 }),
    referingDoctor: varchar('refering_doctor', { length: 50 }),
    xferToPharmacyName: varchar('xfer_to_pharmacy_name', { length: 50 }),
    wingCode2: varchar('wing_code2', { length: 50 }),
    xferToPharmacyAddress1: varchar('xfer_to_pharmacy_address1', {
      length: 255
    }),
    xferToPharmacyAddress2: varchar('xfer_to_pharmacy_address2', {
      length: 255
    }),
    xferToPharmacyCity: varchar('xfer_to_pharmacy_city', { length: 50 }),
    xferToPharmacyPhone: varchar('xfer_to_pharmacy_phone', { length: 20 }),
    xferToPharmacyNpi: varchar('xfer_to_pharmacy_npi', { length: 30 }),
    xferToPharmacyNcpdp: varchar('xfer_to_pharmacy_ncpdp', { length: 30 }),
    billStatus: varchar('bill_status', { length: 50 }),
    xferToPharmacyDea: varchar('xfer_to_pharmacy_dea', { length: 30 }),
    billStatusText: varchar('bill_status_text', { length: 50 }),
    workflowStatus: varchar('workflow_status', { length: 50 }),
    prescribedDrug: varchar('prescribed_drug', { length: 50 }),
    workflowStatusText: varchar('workflow_status_text', { length: 50 }),
    claimAuthorizationNumber: varchar('claim_authorization_number', {
      length: 50
    }),
    priorAuthNumber: varchar('prior_auth_number', { length: 50 }),
    electionPrescriptionOriginTime: varchar(
      'election_prescription_origin_time',
      { length: 50 }
    )
  },
  (table) => [
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'mms_prescriptions_patient_id_fkey'
    })
  ]
);

export const onehealthLabOrders = pgTable(
  'onehealth_lab_orders',
  {
    id: serial().primaryKey().notNull(),
    orderGuid: varchar('order_guid', { length: 50 }),
    userId: integer('user_id'),
    registeredKitId: varchar('registered_kit_id', { length: 50 }),
    testingKitType: varchar('testing_kit_type', { length: 100 }).default(''),
    totalQuantity: integer('total_quantity').default(1).notNull(),
    usePrescriptionService: boolean('use_prescription_service').default(false),
    interval: varchar({ length: 30 }),
    resultsNeedsRevision: boolean('results_needs_revision').default(false),
    lab: varchar({ length: 30 }).default('rucdr'),
    additionalData: varchar('additional_data', { length: 30 }).default('rucdr'),
    postedData: text('posted_data'),
    company: varchar({ length: 30 }).default('Ravkoo'),
    returnMailer: varchar('return_mailer', { length: 255 }).default(''),
    insuranceEnabled: varchar('insurance_enabled', { length: 255 }).default(''),
    kitIds: varchar('kit_ids', { length: 255 }).default(''),
    patientId: varchar('patient_id', { length: 50 }).default(''),
    hcpId: varchar('hcp_id', { length: 50 }).default(''),
    testCode: varchar('test_code', { length: 50 }).default(''),
    askOnEntry: varchar({ length: 50 }).default(''),
    diagnosticCode: varchar('diagnostic_code', { length: 255 }).default(''),
    diagnoseObservationDate: timestamp('diagnose_observation_date', {
      withTimezone: true,
      mode: 'string'
    }),
    labRefId: varchar('lab_ref_id', { length: 255 }).default(''),
    status: enumOnehealthLabOrdersStatus().default('PENDING'),
    paymentStatus:
      enumOnehealthLabOrdersPaymentStatus('payment_status').default('PENDING'),
    oneHealth: text('one_health'),
    collectionAt: timestamp('collection_at', {
      withTimezone: true,
      mode: 'string'
    }),
    kitRegisterAt: timestamp('kit_register_at', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    serviceCharges: numeric('service_charges').default('0'),
    shippingAmount: numeric('shipping_amount').default('0'),
    totalCost: numeric('total_cost').default('0'),
    labId: varchar('lab_id', { length: 30 }).default(sql`NULL`),
    paymentTransaction: text('payment_transaction'),
    flatPrice: numeric('flat_price').default('0'),
    name: varchar({ length: 150 }).default(''),
    productCode: varchar('product_code', { length: 100 }).default(''),
    discount: numeric().default('0')
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'onehealth_lab_orders_user_id_fkey'
    }),
    unique('onehealth_lab_orders_order_guid_key').on(table.orderGuid)
  ]
);

export const patientInsurances = pgTable(
  'patient_insurances',
  {
    patientInsuranceId: serial('patient_insurance_id').primaryKey().notNull(),
    patientId: integer('patient_id').notNull(),
    insuranceId: varchar('insurance_id', { length: 255 }),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'patient_insurances_patient_id_fkey'
    })
  ]
);

export const permissionGroups = pgTable(
  'permission_groups',
  {
    patientId: integer('patient_id').notNull(),
    associatedUserId: integer('associated_user_id').notNull(),
    groupId: serial('group_id').primaryKey().notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.associatedUserId],
      foreignColumns: [users.userId],
      name: 'permission_groups_associated_user_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'permission_groups_patient_id_fkey'
    }),
    unique('permission_groups_patient_id_associated_user_id_key').on(
      table.patientId,
      table.associatedUserId
    )
  ]
);

export const telehealthServiceOrder: any = pgTable(
  'telehealth_service_order',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    answerGivenBy: integer('answer_given_by'),
    providerId: integer('provider_id'),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }),
    status: enumTelehealthServiceOrderStatus(),
    prescriptionDelivery: boolean('prescription_delivery').default(false),
    ravkooPrescriptionOption: boolean('ravkoo_prescription_option').default(
      false
    ),
    pharmacyName: varchar('pharmacy_name', { length: 100 }).default(''),
    pharmacyPhone: varchar('pharmacy_phone', { length: 20 }).default(''),
    pharmacyAddress: varchar('pharmacy_address', { length: 255 }).default(''),
    serviceType:
      enumTelehealthServiceOrderServiceType('service_type').default('SYNC'),
    releaseMedical: boolean('release_medical').default(false),
    releaseMedicalAt: timestamp('release_medical_at', {
      withTimezone: true,
      mode: 'string'
    }),
    isCancelledByProvider: boolean('is_cancelled_by_provider').default(false),
    cancelledAt: timestamp('cancelled_at', {
      withTimezone: true,
      mode: 'string'
    }),
    pharmacyCity: varchar('pharmacy_city', { length: 20 }),
    pharmacyState: varchar('pharmacy_state', { length: 20 }),
    pharmacyZip: varchar('pharmacy_zip', { length: 20 }),
    pharmacyFax: varchar('pharmacy_fax', { length: 20 }),
    pharmacyPreference: integer('pharmacy_preference').default(0).notNull(),
    claimType: enumTelehealthServiceOrderClaimType('claim_type'),
    claimId: varchar('claim_id', { length: 255 }),
    payorName: varchar('payor_name', { length: 255 }),
    injuryDate: timestamp('injury_date', {
      withTimezone: true,
      mode: 'string'
    }),
    currentMedicines: varchar('current_medicines', { length: 255 }),
    allergiesToMedicines: varchar('allergies_to_medicines', { length: 255 }),
    otherAllergies: varchar('other_allergies', { length: 255 }),
    doctorNotes: varchar('doctor_notes', { length: 255 }),
    abnormalFindings: varchar('abnormal_findings', { length: 255 }),
    termsAndConditionsAccepted: boolean(
      'terms_and_conditions_accepted'
    ).default(false),
    cancellationReason: varchar('cancellation_reason', { length: 255 }),
    isRefillRequest: boolean('is_refill_request').default(false),
    externalOrderId: varchar('external_order_id', { length: 50 }),
    followUp: integer('follow_up'),
    visitType: enumTelehealthServiceOrderVisitType('visit_type'),
    completionReason: varchar('completion_reason', { length: 255 }),
    sessionType: varchar('session_type', { length: 255 }),
    scheduleType: varchar('schedule_type', { length: 255 }),
    affiliateid: varchar({ length: 255 }),
    clientName: varchar('client_name', { length: 255 }),
    dosespotPharmacyId: varchar('dosespot_pharmacy_id', { length: 200 }),
    pharmacyNcpdpId: varchar('pharmacy_ncpdp_id', { length: 255 }),
    erxPrescriptionVisitiedAt: timestamp('erx_prescription_visitied_at', {
      withTimezone: true,
      mode: 'string'
    }),
    completedAt: timestamp('completed_at', {
      withTimezone: true,
      mode: 'string'
    }),
    pharmacyId: integer('pharmacy_id')
  },
  (table) => [
    foreignKey({
      columns: [table.answerGivenBy],
      foreignColumns: [users.userId],
      name: 'telehealth_service_order_answer_given_by_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'telehealth_service_order_order_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'telehealth_service_order_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_order_service_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'telehealth_service_order_pharmacy_id_fkey'
    }),
    unique('telehealth_service_order_order_guid_key').on(table.orderGuid)
  ]
);

export const orders = pgTable(
  'orders',
  {
    id: serial().primaryKey().notNull(),
    orderId: text('order_id').notNull(),
    scheduleId: integer('schedule_id'),
    startTime: timestamp('start_time', { withTimezone: true, mode: 'string' }),
    endTime: timestamp('end_time', { withTimezone: true, mode: 'string' }),
    callerId: integer('caller_id'),
    calleeId: integer('callee_id'),
    doctorId: integer('doctor_id'),
    status: enumOrdersStatus(),
    type: enumOrdersType(),
    category: enumOrdersCategory(),
    conversationMode: text('conversation_mode'),
    cost: numeric(),
    billed: boolean().default(false),
    callerLocation: varchar('caller_location', { length: 255 }),
    calleeLocation: varchar('callee_location', { length: 255 }),
    instructions: text(),
    diagnosis: text(),
    procedure: text(),
    visitSummary: text('visit_summary'),
    regenerateVisitSummary: boolean('regenerate_visit_summary').default(false),
    regenerateCcdFile: boolean('regenerate_ccd_file').default(false),
    isVirtualRoom: boolean('is_virtual_room').default(false),
    hideVisitDetails: boolean('hide_visit_details').default(false),
    audioStreamScreenshot: text('audio_stream_screenshot'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    currency: text(),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    duration: varchar({ length: 50 }),
    hostPassPhrase: varchar('host_pass_phrase', { length: 255 }),
    viewerPassPhrase: varchar('viewer_pass_phrase', { length: 255 }),
    channel: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.calleeId],
      foreignColumns: [users.userId],
      name: 'orders_callee_id_fkey'
    }),
    foreignKey({
      columns: [table.callerId],
      foreignColumns: [users.userId],
      name: 'orders_caller_id_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'orders_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'orders_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.scheduleId],
      foreignColumns: [schedules.scheduleId],
      name: 'orders_schedule_id_fkey'
    }),
    unique('orders_order_id_key').on(table.orderId)
  ]
);

export const practiceGroups = pgTable('practice_groups', {
  practiceGroupId: serial('practice_group_id').primaryKey().notNull(),
  name: text(),
  phones: text(),
  visitAddress: text('visit_address'),
  installType: varchar('install_type', { length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const mobileNumberOtpValidator = pgTable('mobile_number_otp_validator', {
  id: uuid().primaryKey().notNull(),
  phone: varchar({ length: 20 }).notNull(),
  otp: varchar({ length: 1000 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const consultUpdateDetailsHistory = pgTable(
  'consult_update_details_history',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    userId: integer('user_id').notNull(),
    previousValues: varchar('previous_values', { length: 2000 }),
    updatedValues: varchar('updated_values', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'consult_update_details_history_user_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey1'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey2'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey3'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_update_details_history_service_order_id_fkey4'
    })
  ]
);

export const providerLicense = pgTable(
  'provider_license',
  {
    providerLicenseId: serial('provider_license_id').primaryKey().notNull(),
    userId: integer('user_id'),
    licenseNumber: varchar('license_number', { length: 255 }),
    licenseState: integer('license_state'),
    licenseStateName: varchar('license_state_name', { length: 255 }),
    licenseExpirationDate: timestamp('license_expiration_date', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'provider_license_user_id_fkey'
    }),
    foreignKey({
      columns: [table.licenseState],
      foreignColumns: [states.stateId],
      name: 'provider_license_license_state_fkey'
    })
  ]
);

export const requestsLog = pgTable(
  'requests_log',
  {
    requestLogId: serial('request_log_id').primaryKey().notNull(),
    userId: integer('user_id'),
    installType: varchar('install_type', { length: 255 }),
    method: varchar({ length: 255 }).notNull(),
    responseHttpStatus: integer('response_http_status').notNull(),
    endpoint: text().notNull(),
    timestamp: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'requests_log_user_id_fkey'
    })
  ]
);

export const requestObjects = pgTable('request_objects', {
  objectId: serial('object_id').primaryKey().notNull(),
  name: varchar({ length: 255 }).notNull(),
  type: varchar({ length: 255 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const medicines = pgTable('medicines', {
  medicineId: serial('medicine_id').primaryKey().notNull(),
  name: varchar({ length: 50 }).notNull(),
  description: text(),
  brand: varchar({ length: 500 }),
  form: varchar({ length: 500 }),
  dosage: varchar({ length: 500 }),
  quantity: varchar({ length: 500 }),
  refillQuantity: varchar('refill_quantity', { length: 500 }),
  directionQuantity: varchar('direction_quantity', { length: 500 }),
  directionOne: varchar('direction_one', { length: 500 }),
  directionTwo: varchar('direction_two', { length: 500 }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
});

export const medicineServicePharmacyMapping = pgTable(
  'medicine_service_pharmacy_mapping',
  {
    id: serial().primaryKey().notNull(),
    medicineId: integer('medicine_id').notNull(),
    pharmacyId: integer('pharmacy_id').notNull(),
    serviceId: integer('service_id').notNull(),
    status: boolean().default(true)
  },
  (table) => [
    foreignKey({
      columns: [table.medicineId],
      foreignColumns: [medicines.medicineId],
      name: 'medicine_service_pharmacy_mapping_medicine_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'medicine_service_pharmacy_mapping_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'medicine_service_pharmacy_mapping_service_id_fkey'
    })
  ]
);

export const chatRooms = pgTable('chat_rooms', {
  id: serial().primaryKey().notNull(),
  roomName: text('room_name').notNull(),
  roomIdentifier: uuid('room_identifier').defaultRandom(),
  deleted: boolean().default(false).notNull(),
  deletedAt: timestamp('deleted_at', { mode: 'string' }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  serviceKey: text('service_key'),
  description: text(),
  lastMessageAt: timestamp('last_message_at', { mode: 'string' }),
  lastMessage: text('last_message')
});

export const chatRoomMembers = pgTable(
  'chat_room_members',
  {
    id: serial().primaryKey().notNull(),
    userId: serial('user_id').notNull(),
    roomId: serial('room_id').notNull(),
    deleted: boolean().default(false).notNull(),
    deletedAt: timestamp('deleted_at', { mode: 'string' }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'chat_room_members_user_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_room_members_room_id_chat_rooms_id_fk'
    })
  ]
);

export const subscriptionPlans = pgTable(
  'subscription_plans',
  {
    planId: serial('plan_id').primaryKey().notNull(),
    name: text().notNull(),
    description: text(),
    price: integer().notNull(),
    currency: text().notNull(),
    billingCycle: enumSubscriptionPlansBillingCycle('billing_cycle').notNull(),
    billingInterval: integer('billing_interval').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    paypalPlanId: text('paypal_plan_id'),
    serviceId: integer('service_id'),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    recurlyPlanId: varchar('recurly_plan_id', { length: 255 }),
    stripePlanId: varchar('stripe_plan_id', { length: 255 }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deletedBy: integer('deleted_by'),
    visitCount: integer('visit_count').default(1)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'subscription_plans_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.deletedBy],
      foreignColumns: [users.userId],
      name: 'subscription_plans_deleted_by_fkey'
    })
  ]
);

export const specialDiscounts = pgTable('special_discounts', {
  discountId: serial('discount_id').primaryKey().notNull(),
  name: text().notNull(),
  description: text(),
  discountPrice: integer('discount_price').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const stripeUserPaymentDetails = pgTable(
  'stripe_user_payment_details',
  {
    id: serial().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    paymentMethodId: text('payment_method_id'),
    offSessionPaymentAllowed: boolean('off_session_payment_allowed'),
    paymentStatus: enumStripeUserPaymentDetailsPaymentStatus('payment_status')
      .default('success')
      .notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'stripe_user_payment_details_user_id_fkey'
    })
  ]
);

export const telehealthServiceProcedureCodesMapping = pgTable(
  'telehealth_service_procedure_codes_mapping',
  {
    telehealthServiceProcedureCodesMappingId: serial(
      'telehealth_service_procedure_codes_mapping_id'
    )
      .primaryKey()
      .notNull(),
    serviceId: integer('service_id').notNull(),
    procedureCodeId: integer('procedure_code_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_procedure_codes_mapping_service_id_fkey'
    }),
    foreignKey({
      columns: [table.procedureCodeId],
      foreignColumns: [procedureCodes.procedureCodeId],
      name: 'telehealth_service_procedure_codes_mappi_procedure_code_id_fkey'
    })
  ]
);

export const telehealthServiceQuestions = pgTable(
  'telehealth_service_questions',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    question: varchar({ length: 500 }).notNull(),
    helpText: varchar('help_text', { length: 200 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    selectionOption: text('selection_option'),
    questionType:
      enumTelehealthServiceQuestionsQuestionType('question_type').default(
        'YesNo'
      ),
    displayOrder: integer('display_order').default(1),
    parentId: integer('parent_id'),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    haltOnSelectionOption: varchar('halt_on_selection_option', { length: 500 }),
    isPreQuestions: boolean('is_pre_questions').default(false),
    isOptional: boolean('is_optional').default(false),
    questionFor:
      enumTelehealthServiceQuestionsQuestionFor('question_for').default('both')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_questions_service_id_fkey'
    })
  ]
);

export const telehealthServiceProviderMapping = pgTable(
  'telehealth_service_provider_mapping',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    providerId: integer('provider_id'),
    costPrice: numeric('cost_price').default('0'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    status: boolean().default(true)
  },
  (table) => [
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'telehealth_service_provider_mapping_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_provider_mapping_service_id_fkey'
    })
  ]
);

export const transactions = pgTable(
  'transactions',
  {
    transactionId: text('transaction_id').primaryKey().notNull(),
    payerUserId: integer('payer_user_id').notNull(),
    payeeUserId: integer('payee_user_id'),
    amount: doublePrecision().notNull(),
    currency: text().notNull(),
    status: enumTransactionsStatus().notNull(),
    description: text(),
    errorDescription: text('error_description'),
    cardDetails: text('card_details'),
    lineItems: text('line_items'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    globalId: varchar('global_id', { length: 100 }),
    refundTransactionId: varchar('refund_transaction_id', { length: 100 }),
    refundCreatedAt: timestamp('refund_created_at', {
      withTimezone: true,
      mode: 'string'
    }),
    refundGlobalId: varchar('refund_global_id', { length: 100 }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    paymentMethodType: enumTransactionsPaymentMethodType(
      'payment_method_type'
    ).default('BRAINTREE'),
    refundPaymentStatus: enumTransactionsRefundPaymentStatus(
      'refund_payment_status'
    ).default('n/a'),
    refundPaymentAmount: doublePrecision('refund_payment_amount').default(0),
    refundErrorDescription: text('refund_error_description'),
    refundSuccessResponse: text('refund_success_response'),
    transactionStatus: varchar('transaction_status', { length: 100 }),
    paymentDetailsId: integer('payment_details_id')
  },
  (table) => [
    foreignKey({
      columns: [table.payeeUserId],
      foreignColumns: [users.userId],
      name: 'transactions_payee_user_id_fkey'
    }),
    foreignKey({
      columns: [table.payerUserId],
      foreignColumns: [users.userId],
      name: 'transactions_payer_user_id_fkey'
    }),
    foreignKey({
      columns: [table.paymentDetailsId],
      foreignColumns: [paymentDetails.paymentDetailsId],
      name: 'transactions_payment_details_id_fkey'
    })
  ]
);

export const stripeUserDetails = pgTable(
  'stripe_user_details',
  {
    userId: integer('user_id').primaryKey().notNull(),
    stripeUserId: text('stripe_user_id').notNull(),
    ccStatus:
      enumStripeUserDetailsCcStatus('cc_status').default('not_captured'),
    oauthStatus:
      enumStripeUserDetailsOauthStatus('oauth_status').default('not_connected'),
    stripeAccountId: text('stripe_account_id'),
    oauthVerificationToken: text('oauth_verification_token'),
    currencyCode: text('currency_code'),
    defaultPaymentMethod: text('default_payment_method'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'stripe_user_details_user_id_fkey'
    })
  ]
);

export const socialHistoryValues = pgTable(
  'social_history_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'social_history_values_summary_id_fkey'
    })
  ]
);

export const userParticlehealth = pgTable(
  'user_particlehealth',
  {
    healthId: serial('health_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    queryId: varchar('query_id', { length: 255 }),
    status: varchar({ length: 255 }),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    buserId: integer('buser_id').notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_particlehealth_user_id_fkey'
    }),
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'user_particlehealth_buser_id_fkey'
    })
  ]
);

export const userDetails = pgTable(
  'user_details',
  {
    userDetailId: serial('user_detail_id').primaryKey().notNull(),
    userId: integer('user_id'),
    data: text(),
    signature: varchar({ length: 255 }),
    specialty: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    address: text(),
    deleted: boolean().default(false),
    bio: text(),
    language: json().default([])
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_details_user_id_fkey'
    })
  ]
);

export const userEducationalVideos = pgTable(
  'user_educational_videos',
  {
    userVideoId: serial('user_video_id').primaryKey().notNull(),
    videoId: serial('video_id').notNull(),
    referredBy: integer('referred_by').notNull(),
    referredFor: integer('referred_for').notNull(),
    doctorId: integer('doctor_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    viewedAt: timestamp('viewed_at', { withTimezone: true, mode: 'string' }),
    viewed: boolean()
  },
  (table) => [
    foreignKey({
      columns: [table.referredBy],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_assigned_by_fkey'
    }),
    foreignKey({
      columns: [table.referredFor],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_assigned_to_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_educational_videos_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.videoId],
      foreignColumns: [educationalVideos.videoId],
      name: 'user_educational_videos_video_id_fkey'
    })
  ]
);

export const products = pgTable('products', {
  productId: serial('product_id').primaryKey().notNull(),
  productName: varchar('product_name', { length: 200 }).notNull(),
  description: varchar({ length: 2000 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false)
});

export const userIdentities = pgTable(
  'user_identities',
  {
    userIdentityId: serial('user_identity_id').primaryKey().notNull(),
    userId: integer('user_id'),
    identifier: text(),
    type: varchar({ length: 255 }),
    installType: varchar('install_type', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_identities_user_id_fkey'
    })
  ]
);

export const followUpReminder = pgTable(
  'follow_up_reminder',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    userId: integer('user_id'),
    lastFollowUpSent: varchar('last_follow_up_sent', { length: 50 }),
    errorMessage: varchar('error_message', { length: 200 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    nextFollowUp: timestamp('next_follow_up', {
      withTimezone: true,
      mode: 'string'
    }),
    status: enumFollowUpReminderStatus().default('pending'),
    tennantId: integer('tennant_id'),
    nextOrderId: integer('next_order_id'),
    followUpSentDate: timestamp('follow_up_sent_date', {
      withTimezone: true,
      mode: 'string'
    })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'follow_up_reminder_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'follow_up_reminder_user_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'follow_up_reminder_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.nextOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'follow_up_reminder_next_order_id_fkey'
    })
  ]
);

export const userFileRepoDetails = pgTable(
  'user_file_repo_details',
  {
    repoId: serial('repo_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    uploadType: enumUserFileRepoDetailsUploadType('upload_type').notNull(),
    connectionDetails: text('connection_details'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_file_repo_details_user_id_fkey'
    })
  ]
);

export const userHealthSummary = pgTable(
  'user_health_summary',
  {
    summaryId: serial('summary_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    type: varchar({ length: 255 }),
    data: text(),
    sourcePlatform: varchar('source_platform', { length: 255 }),
    hasDetail: boolean('has_detail').default(false),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_health_summary_user_id_fkey'
    })
  ]
);

export const userSubscriptionBilling = pgTable(
  'user_subscription_billing',
  {
    billingId: serial('billing_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    planId: integer('plan_id').notNull(),
    basePlanPrice: integer('base_plan_price').notNull(),
    discount: integer().notNull(),
    invoiceNumber: text('invoice_number'),
    specialDiscounts: text('special_discounts'),
    addons: text(),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    totalPrice: text('total_price').default('0'),
    billed: boolean().default(false),
    invoicePdfPath: text('invoice_pdf_path'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.planId],
      foreignColumns: [subscriptionPlans.planId],
      name: 'user_subscription_billing_plan_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_subscription_billing_user_id_fkey'
    })
  ]
);

export const drugDays = pgTable(
  'drug_days',
  {
    id: serial().primaryKey().notNull(),
    drugId: integer('drug_id'),
    drugDays: integer('drug_days').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.drugId],
      foreignColumns: [drugs.drugId],
      name: 'drug_days_drug_id_fkey'
    })
  ]
);

export const userSubscription = pgTable(
  'user_subscription',
  {
    userSubscriptionId: serial('user_subscription_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    planId: integer('plan_id').notNull(),
    startDate: timestamp('start_date', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    noOfIntervals: integer('no_of_intervals'),
    referralCode: text('referral_code'),
    discount: integer().default(0),
    nextBillingDate: timestamp('next_billing_date', {
      withTimezone: true,
      mode: 'string'
    }),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    subscriptionId: text('subscription_id'),
    status: text(),
    serviceId: integer('service_id'),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    consultLimit: integer('consult_limit'),
    consultCount: integer('consult_count')
  },
  (table) => [
    foreignKey({
      columns: [table.planId],
      foreignColumns: [subscriptionPlans.planId],
      name: 'user_subscription_plan_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_subscription_user_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'user_subscription_service_master_id_fkey'
    })
  ]
);

export const consultOrderFiles = pgTable(
  'consult_order_files',
  {
    fileId: serial('file_id').primaryKey().notNull(),
    orderId: integer('order_id'),
    type: enumConsultOrderFilesType(),
    filePath: text('file_path').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    tennantId: integer('tennant_id'),
    fileKey: varchar('file_key', { length: 255 })
      .default('consult_file')
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_order_files_order_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'consult_order_files_tennant_id_fkey'
    })
  ]
);

export const smsTemplate = pgTable(
  'sms_template',
  {
    smsTemplateId: serial('sms_template_id').primaryKey().notNull(),
    smsAction: varchar('sms_action', { length: 100 }).notNull(),
    smsTemplateName: varchar('sms_template_name', { length: 500 }).notNull(),
    serviceId: integer('service_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'sms_template_service_id_fkey'
    })
  ]
);

export const userVitalsDocuments = pgTable(
  'user_vitals_documents',
  {
    userVitalsDocumentId: serial('user_vitals_document_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id'),
    doctorId: integer('doctor_id'),
    path: text(),
    startTime: timestamp('start_time', { withTimezone: true, mode: 'string' }),
    endTime: timestamp('end_time', { withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_vitals_documents_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_vitals_documents_user_id_fkey'
    })
  ]
);

export const chatMessages = pgTable(
  'chat_messages',
  {
    id: serial().primaryKey().notNull(),
    message: text(),
    senderId: serial('sender_id').notNull(),
    roomId: serial('room_id').notNull(),
    deleted: boolean().default(false).notNull(),
    deletedAt: timestamp('deleted_at', { mode: 'string' }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    fileId: integer('file_id'),
    readBy: text('read_by').array().default(['RAY']).notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.senderId],
      foreignColumns: [users.userId],
      name: 'chat_messages_sender_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_messages_room_id_chat_rooms_id_fk'
    })
  ]
);

export const jobs = pgTable(
  'jobs',
  {
    id: serial().primaryKey().notNull(),
    path: text(),
    orderId: integer('order_id').notNull(),
    referralId: integer('referral_id').notNull(),
    status: enumJobsStatus().default('pending'),
    failedMessage: text('failed_message'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'jobs_order_id_fkey'
    }),
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'jobs_referral_id_fkey'
    })
  ]
);

export const userSchedules = pgTable(
  'user_schedules',
  {
    id: uuid().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    isAvailable: boolean('is_available').default(true),
    endDatetime: timestamp('end_datetime', {
      withTimezone: true,
      mode: 'string'
    }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    createdBy: integer('created_by'),
    updatedBy: integer('updated_by'),
    deletedBy: integer('deleted_by'),
    scheduleDate: varchar('schedule_date', { length: 30 })
      .default('')
      .notNull(),
    startDatetime: timestamp('start_datetime', {
      withTimezone: true,
      mode: 'string'
    })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_schedules_user_id_fkey'
    }),
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_created_by_fkey'
    }),
    foreignKey({
      columns: [table.updatedBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_updated_by_fkey'
    }),
    foreignKey({
      columns: [table.deletedBy],
      foreignColumns: [users.userId],
      name: 'user_schedules_deleted_by_fkey'
    })
  ]
);

export const chatFiles = pgTable(
  'chat_files',
  {
    fileId: serial('file_id').primaryKey().notNull(),
    userId: serial('user_id').notNull(),
    roomId: serial('room_id').notNull(),
    name: text(),
    path: text(),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'chat_files_user_id_users_user_id_fk'
    }),
    foreignKey({
      columns: [table.roomId],
      foreignColumns: [chatRooms.id],
      name: 'chat_files_room_id_chat_rooms_id_fk'
    })
  ]
);

export const emailTemplate = pgTable(
  'email_template',
  {
    emailTemplateId: serial('email_template_id').primaryKey().notNull(),
    emailAction: varchar('email_action', { length: 100 }).notNull(),
    emailTemplateName: varchar('email_template_name', {
      length: 500
    }).notNull(),
    serviceId: integer('service_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'email_template_service_id_fkey'
    })
  ]
);

export const prescriptionPreference = pgTable(
  'prescription_preference',
  {
    preferenceId: serial('preference_id').primaryKey().notNull(),
    pharmacyId: integer('pharmacy_id'),
    tennantId: integer('tennant_id'),
    pharmacyName: varchar('pharmacy_name', { length: 100 }),
    clientName: varchar('client_name', { length: 100 }),
    preference: enumPrescriptionPreferencePreference().default('fax'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'prescription_preference_pharmacy_id_fkey'
    }),
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'prescription_preference_tennant_id_fkey'
    })
  ]
);

export const lifefileConfiguration = pgTable(
  'lifefile_configuration',
  {
    lifefileConfigId: serial('lifefile_config_id').primaryKey().notNull(),
    tennantId: integer('tennant_id'),
    pharmacyId: integer('pharmacy_id'),
    pharmacyName: varchar('pharmacy_name', { length: 100 }),
    clientName: varchar('client_name', { length: 100 }),
    lifefileUrl: varchar('lifefile_url', { length: 200 }).notNull(),
    apiUsername: varchar('api_username', { length: 100 }).notNull(),
    apiPassword: varchar('api_password', { length: 100 }).notNull(),
    practiceId: integer('practice_id').notNull(),
    practiceName: varchar('practice_name', { length: 100 }).notNull(),
    vendorId: integer('vendor_id').notNull(),
    locationId: integer('location_id').notNull(),
    networkId: integer('network_id').notNull(),
    networkName: varchar('network_name', { length: 100 }).notNull(),
    shippingServices:
      enumLifefileConfigurationShippingServices('shipping_services').default(
        '7780'
      ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'lifefile_configuration_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'lifefile_configuration_pharmacy_id_fkey'
    })
  ]
);

export const telehealthServiceQuestionAnswerDump = pgTable(
  'telehealth_service_question_answer_dump',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    questionText: varchar('question_text', { length: 2000 }).notNull(),
    answer: varchar({ length: 2000 }).default('false'),
    otherText: varchar('other_text', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    selectionOption: text('selection_option'),
    answeredBy: integer('answered_by')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'telehealth_service_question_answer_dump_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.answeredBy],
      foreignColumns: [users.userId],
      name: 'telehealth_service_question_answer_dump_answered_by_fkey'
    })
  ]
);

export const tennantMaster: any = pgTable(
  'tennant_master',
  {
    id: serial().primaryKey().notNull(),
    tennantName: varchar('tennant_name', { length: 255 }).notNull(),
    pharmacyFax: varchar('pharmacy_fax', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    filterDisplay: boolean('filter_display').default(false),
    parentId: integer('parent_id'),
    labFaxNumber: varchar('lab_fax_number', { length: 255 }),
    supportEmail: varchar('support_email', { length: 255 }),
    logoUrl: varchar('logo_url', { length: 255 }),
    paypalClientId: text('paypal_client_id'),
    paypalClientSecret: text('paypal_client_secret'),
    accessLabClientNumber: varchar('access_lab_client_number', {
      length: 2000
    }),
    twilioAccountSid: text('twilio_account_sid'),
    twilioPhoneNumber: text('twilio_phone_number'),
    twilioAuthToken: text('twilio_auth_token'),
    stripeClientId: varchar('stripe_client_id', { length: 2000 }),
    stripeClientSecret: varchar('stripe_client_secret', { length: 2000 }),
    preferredPaymentGateway: enumTennantMasterPreferredPaymentGateway(
      'preferred_payment_gateway'
    ),
    mailchipFromEmail: varchar('mailchip_from_email', { length: 255 }),
    mailchipFromEmailName: varchar('mailchip_from_email_name', { length: 255 }),
    tennantDisplayName: varchar('tennant_display_name', { length: 255 }),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotClientId: varchar('dosespot_client_id', { length: 1000 }),
    dosespotClientSecret: varchar('dosespot_client_secret', { length: 1000 }),
    configuredDomain: varchar('configured_domain', { length: 200 }),
    tennantGuid: uuid('tennant_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    defaultProviderId: integer('default_provider_id'),
    showPoweredBy: boolean('show_powered_by').default(true),
    faviconUrl: varchar('favicon_url', { length: 255 }),
    type: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.id],
      name: 'tennant_master_parent_id_fkey'
    }),
    foreignKey({
      columns: [table.defaultProviderId],
      foreignColumns: [users.userId],
      name: 'tennant_master_default_provider_id_fkey'
    })
  ]
);

export const consultReassignHistory = pgTable(
  'consult_reassign_history',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id').notNull(),
    previousProvider: integer('previous_provider').notNull(),
    updatedProvider: integer('updated_provider').notNull(),
    reassignedBy: integer('reassigned_by').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'consult_reassign_history_service_order_id_fkey'
    }),
    foreignKey({
      columns: [table.previousProvider],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_previous_provider_fkey'
    }),
    foreignKey({
      columns: [table.updatedProvider],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_updated_provider_fkey'
    }),
    foreignKey({
      columns: [table.reassignedBy],
      foreignColumns: [users.userId],
      name: 'consult_reassign_history_reassigned_by_fkey'
    })
  ]
);

export const users = pgTable(
  'users',
  {
    userId: serial('user_id').primaryKey().notNull(),
    userGuid: varchar('user_guid', { length: 255 }),
    password: text(),
    role: enumUsersRole().default('USER'),
    status: enumUsersStatus().default('OFFLINE'),
    installType: varchar('install_type', { length: 255 }),
    firstName: text('first_name'),
    lastName: text('last_name'),
    zipCode: varchar('zip_code', { length: 255 }),
    email: text().notNull(),
    phone: text(),
    otp: text(),
    dob: text(),
    apptLength: integer('appt_length'),
    apptStartTime: varchar('appt_start_time', { length: 255 }),
    apptEndTime: varchar('appt_end_time', { length: 255 }),
    secureMessage: boolean('secure_message').default(false),
    connectionRequests: boolean('connection_requests').default(false),
    vitalsCcdEnabled: boolean('vitals_ccd_enabled').default(false),
    apptRequests: boolean('appt_requests').default(false),
    trialValidity: timestamp('trial_validity', {
      withTimezone: true,
      mode: 'string'
    }),
    isCcCaptured: boolean('is_cc_captured'),
    gender: enumUsersGender().default('male'),
    deleted: boolean().default(false),
    emailVerified: boolean('email_verified').default(false),
    userAvatar: text('user_avatar'),
    idCard: text('id_card'),
    history: text(),
    questionnaire: text(),
    tokenValidity: integer('token_validity'),
    emailVerificationDetails: text('email_verification_details'),
    lastActive: timestamp('last_active', {
      withTimezone: true,
      mode: 'string'
    }),
    locked: timestamp({ withTimezone: true, mode: 'string' }),
    registrationKey: text('registration_key'),
    isRpmEnabled: boolean('is_rpm_enabled').default(false),
    isNotifyOnCapture: boolean('is_notify_on_capture').default(false),
    vitalsThresholds: text('vitals_thresholds'),
    vitalsCron: text('vitals_cron'),
    appDetails: text('app_details'),
    historyUpdatedAt: timestamp('history_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    questionnaireUpdatedAt: timestamp('questionnaire_updated_at', {
      withTimezone: true,
      mode: 'string'
    }),
    debug: boolean().default(false),
    invitationCodeValidity: integer('invitation_code_validity'),
    cronExpression: text('cron_expression'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    mirthCcdEnabled: boolean('mirth_ccd_enabled').default(false),
    ccPaymentAccepted: boolean('cc_payment_accepted').default(false),
    recordingEnabled: boolean('recording_enabled').default(true),
    transcriptionEnabled: boolean('transcription_enabled').default(true),
    ordersEnabled: boolean('orders_enabled').default(true),
    showHealthSummaries: boolean('show_health_summaries'),
    healthgorillaId: varchar('healthgorilla_id', { length: 255 }),
    releaseMedical: boolean('release_medical'),
    telehealthServiceCost: numeric('telehealth_service_cost').default('0'),
    subRole: enumUsersSubRole('sub_role').default('USER'),
    myInviteCode: varchar('my_invite_code', { length: 15 }).default(sql`NULL`),
    referredByInviteCode: varchar('referred_by_invite_code', {
      length: 15
    }).default(sql`NULL`),
    referredByUserId: integer('referred_by_user_id'),
    email2: text('email_2'),
    tennantId: integer('tennant_id'),
    secondaryPhone: text('secondary_phone'),
    isTennantOwner: boolean('is_tennant_owner').default(false).notNull(),
    sendEmailCampaign: boolean('send_email_campaign').default(false).notNull(),
    appTimezone: varchar('app_timezone', { length: 255 }).default('{}'),
    rxPersonId: varchar({ length: 50 }),
    rxStatus: boolean().default(false),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotApiResponse: varchar('dosespot_api_response', { length: 2000 }),
    idCardFile: text('id_card_file'),
    pharmacyAccess: varchar('pharmacy_access', { length: 500 }),
    parentId: integer('parent_id'),
    dependentAccountRelation: varchar('dependent_account_relation', {
      length: 255
    }),
    trackingCode: varchar('tracking_code', { length: 255 }),
    userPrivateKey: text('user_private_key')
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'users_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.userId],
      name: 'users_parent_id_fkey'
    }),
    unique('users_my_invite_code_key').on(table.myInviteCode)
  ]
);

export const externalRequestsLog = pgTable(
  'external_requests_log',
  {
    externalRequestLogId: serial('external_request_log_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id'),
    type: varchar({ length: 255 }).notNull(),
    detail: text().notNull(),
    timestamp: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'external_requests_log_user_id_fkey'
    })
  ]
);

export const mmsPatientInvitations = pgTable(
  'mms_patient_invitations',
  {
    invitationId: serial('invitation_id').primaryKey().notNull(),
    email: text(),
    phone: text(),
    code: text().notNull(),
    mmsPatientId: integer('mms_patient_id').notNull(),
    accepted: boolean().default(false),
    expiry: timestamp({ withTimezone: true, mode: 'string' }),
    remainingTries: integer('remaining_tries').default(3),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.mmsPatientId],
      foreignColumns: [mmsPatients.patientId],
      name: 'mms_patient_invitations_mms_patient_id_fkey'
    })
  ]
);

export const pharmacies = pgTable(
  'pharmacies',
  {
    pharmacyId: serial('pharmacy_id').primaryKey().notNull(),
    npi: varchar({ length: 255 }),
    name: varchar({ length: 255 }),
    nabp: varchar({ length: 255 }),
    address: text(),
    phone: varchar({ length: 255 }),
    fax: varchar({ length: 255 }),
    email: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    ncpdpid: varchar({ length: 50 }),
    pharmcistName: varchar('pharmcist_name', { length: 50 }),
    dea: varchar({ length: 50 }),
    pharmacyLegalName: varchar('pharmacy_legal_name', { length: 100 }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false).notNull(),
    pharmaciesGuid: uuid('pharmacies_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull()
  },
  (table) => [unique('pharmacies_npi_key').on(table.npi)]
);

export const servicePaymentMapping = pgTable(
  'service_payment_mapping',
  {
    servicePaymentMappingId: serial('service_payment_mapping_id')
      .primaryKey()
      .notNull(),
    serviceKey: varchar('service_key', { length: 255 }),
    serviceMasterId: integer('service_master_id'),
    paymentDetailsId: integer('payment_details_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    paymentType:
      enumServicePaymentMappingPaymentType('payment_type').default(
        'subscription'
      )
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'service_payment_mapping_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.paymentDetailsId],
      foreignColumns: [paymentDetails.paymentDetailsId],
      name: 'service_payment_mapping_payment_details_id_fkey'
    })
  ]
);

export const referralTracking = pgTable(
  'referral_tracking',
  {
    id: serial().primaryKey().notNull(),
    referralId: integer('referral_id'),
    orderGuid: varchar('order_guid', { length: 50 }),
    shippingPartner: enumReferralTrackingShippingPartner('shipping_partner'),
    trackingNumber: varchar('tracking_number', { length: 500 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'referral_tracking_referral_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'referral_tracking_order_guid_fkey'
    })
  ]
);

export const supportNotes = pgTable(
  'support_notes',
  {
    id: uuid().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    supportUserId: integer('support_user_id').notNull(),
    orderGuid: varchar('order_guid', { length: 50 }),
    generalNote: text('general_note').default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'support_notes_user_id_fkey'
    }),
    foreignKey({
      columns: [table.supportUserId],
      foreignColumns: [users.userId],
      name: 'support_notes_support_user_id_fkey'
    }),
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'support_notes_order_guid_fkey'
    })
  ]
);

export const telehealthServices: any = pgTable(
  'telehealth_services',
  {
    id: serial().primaryKey().notNull(),
    serviceName: varchar('service_name', { length: 200 }).notNull(),
    description: varchar({ length: 2000 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    serviceType:
      enumTelehealthServicesServiceType('service_type').default('BUSER'),
    displayOrder: integer('display_order').default(1),
    serviceMode:
      enumTelehealthServicesServiceMode('service_mode').default(
        'BOTH_SYNC_ASYNC'
      ),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    amount: integer(),
    tenantId: integer('tenant_id'),
    paypalPlanId: text('paypal_plan_id'),
    title: varchar({ length: 255 }),
    subtitle: varchar({ length: 2000 }),
    serviceKey: varchar('service_key', { length: 255 }),
    sessionType: varchar('session_type', { length: 255 }),
    fieldsOptions: varchar('fields_options', { length: 255 }),
    onCompleteScript: varchar('on_complete_script', { length: 2000 }),
    onFollowUpScript: varchar('on_follow_up_script', { length: 2000 }),
    accessLabsTestCode: varchar('access_labs_test_code', { length: 2000 }),
    onCreateScript: varchar('on_create_script', { length: 2000 }),
    onLabResultReceivedScript: varchar('on_lab_result_received_script', {
      length: 2000
    }),
    onUpdateScheduleScript: varchar('on_update_schedule_script', {
      length: 2000
    }),
    disclaimer: text(),
    serviceDetails: text('service_details'),
    isVideoCall: boolean('is_video_call').default(true).notNull(),
    isAudioCall: boolean('is_audio_call').default(true).notNull(),
    displayQuestionnaire: boolean('display_questionnaire')
      .default(false)
      .notNull(),
    displayServiceName: text('display_service_name'),
    erxDrugKey: varchar('erx_drug_key', { length: 255 }),
    filterDisplay: boolean('filter_display').default(false).notNull(),
    servicesGuid: uuid('services_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    originalAmount: varchar('original_amount', { length: 255 }),
    durationText: varchar('duration_text', { length: 255 }),
    discountText: varchar('discount_text', { length: 255 }),
    consultInstruction: json('consult_instruction'),
    serviceInstruction: json('service_instruction'),
    serviceMasterId: integer('service_master_id'),
    nextServiceId: integer('next_service_id'),
    eligibleMessages: json('eligible_messages'),
    displayAmount: varchar('display_amount', { length: 255 }).default(
      sql`NULL`
    ),
    userConsent: text('user_consent'),
    preferredPharmacySelection: varchar('preferred_pharmacy_selection', {
      length: 255
    }),
    nextFollowupDays: integer('next_followup_days')
  },
  (table) => [
    foreignKey({
      columns: [table.serviceMasterId],
      foreignColumns: [telehealthServiceMaster.id],
      name: 'telehealth_services_service_master_id_fkey'
    }),
    foreignKey({
      columns: [table.nextServiceId],
      foreignColumns: [table.id],
      name: 'telehealth_services_next_service_id_fkey'
    }),
    unique('telehealth_services_service_name_key').on(table.serviceName)
  ]
);

export const tennantConfig = pgTable(
  'tennant_config',
  {
    id: serial().primaryKey().notNull(),
    tennantId: integer('tennant_id'),
    themeColor: json('theme_color'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    themeConfig: json('theme_config'),
    isExternal: boolean('is_external').default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'tennant_config_tennant_id_fkey'
    })
  ]
);

export const telehealthServiceMaster = pgTable(
  'telehealth_service_master',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 200 }).notNull(),
    description: varchar({ length: 500 }),
    serviceMasterGuid: uuid('service_master_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    tennantId: integer('tennant_id'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    serviceKey: varchar('service_key', { length: 500 }),
    initialServiceId: integer('initial_service_id'),
    categories: integer().array().default([]),
    filterDisplay: boolean('filter_display').default(true).notNull(),
    icons: varchar({ length: 500 }).default(sql`NULL`),
    icon: varchar({ length: 500 }).default(sql`NULL`)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'telehealth_service_master_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.initialServiceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_master_initial_service_id_fkey'
    })
  ]
);

export const tenantAuthProvider = pgTable(
  'tenant_auth_provider',
  {
    authId: serial('auth_id').primaryKey().notNull(),
    tenantId: integer('tenant_id').notNull(),
    secretToken: varchar('secret_token', { length: 255 }).notNull(),
    authToken: varchar('auth_token', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'tenant_auth_provider_tenant_id_fkey'
    })
  ]
);

export const serviceActionPreference = pgTable(
  'service_action_preference',
  {
    serviceActionPreferenceId: serial('service_action_preference_id')
      .primaryKey()
      .notNull(),
    serviceId: integer('service_id'),
    tenantId: integer('tenant_id').notNull(),
    actionType: varchar('action_type', { length: 255 }).notNull(),
    webhookUrl: varchar('webhook_url', { length: 255 }),
    smsTemplate: text('sms_template'),
    emailTemplate: varchar('email_template', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    externalApiIntegration: varchar('external_api_integration', {
      length: 255
    }),
    apiKey: text('api_key'),
    apiSecret: text('api_secret'),
    emailSubject: varchar('email_subject', { length: 255 }),
    ccRecipient: json('cc_recipient').default([]),
    bccRecipient: json('bcc_recipient').default([])
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'service_action_preference_service_id_fkey'
    }),
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'service_action_preference_tenant_id_fkey'
    })
  ]
);

export const diagnosesValues = pgTable(
  'diagnoses_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'diagnoses_values_summary_id_fkey'
    })
  ]
);

export const familyHistoryValues = pgTable(
  'family_history_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'family_history_values_summary_id_fkey'
    })
  ]
);

export const proceduresValues = pgTable(
  'procedures_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'procedures_values_summary_id_fkey'
    })
  ]
);

export const carePlanValues = pgTable(
  'care_plan_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'care_plan_values_summary_id_fkey'
    })
  ]
);

export const resultsValues = pgTable(
  'results_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'results_values_summary_id_fkey'
    })
  ]
);

export const allergiesValues = pgTable(
  'allergies_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'allergies_values_summary_id_fkey'
    })
  ]
);

export const documentValues = pgTable(
  'document_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [userHealthSummary.summaryId],
      name: 'document_values_summary_id_fkey'
    })
  ]
);

export const mmsPrescriptionRefills = pgTable(
  'mms_prescription_refills',
  {
    refillId: serial('refill_id').primaryKey().notNull(),
    prescriptionId: integer('prescription_id').notNull(),
    refillNo: varchar('refill_no', { length: 255 }),
    dateFilled: varchar('date_filled', { length: 255 }),
    datePicked: varchar('date_picked', { length: 255 }),
    qtyOrdered: numeric('qty_ordered'),
    qtyDispensed: numeric('qty_dispensed'),
    notes: text(),
    deliveryMethod: text('delivery_method'),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    costPrice: numeric('cost_price'),
    dispFee: numeric('disp_fee'),
    rxAmount: numeric('rx_amount'),
    patientCopay: numeric('patient_copay'),
    billedAmount: numeric('billed_amount'),
    primaryInsuranceAmount: numeric('primary_insurance_amount'),
    primaryInsuranceCode: varchar('primary_insurance_code', { length: 255 }),
    secondaryInsuranceAmount: numeric('secondary_insurance_amount'),
    tertiaryInsuranceAmount: numeric('tertiary_insurance_amount'),
    status: varchar({ length: 255 })
  },
  (table) => [
    foreignKey({
      columns: [table.prescriptionId],
      foreignColumns: [mmsPrescriptions.prescriptionId],
      name: 'mms_prescription_refills_prescription_id_fkey'
    })
  ]
);

export const vitalsSummaryUploadStatus = pgTable(
  'vitals_summary_upload_status',
  {
    statusId: serial('status_id').primaryKey().notNull(),
    doctorId: integer('doctor_id').notNull(),
    patientId: integer('patient_id').notNull(),
    uploadType:
      enumVitalsSummaryUploadStatusUploadType('upload_type').notNull(),
    uploadStatus: enumVitalsSummaryUploadStatusUploadStatus('upload_status')
      .default('SUCCESS')
      .notNull(),
    docPath: text('doc_path'),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    faxId: integer('fax_id'),
    faxStatus: text('fax_status'),
    error: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'vitals_summary_upload_status_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'vitals_summary_upload_status_patient_id_fkey'
    })
  ]
);

export const vitalsNotificationCycle = pgTable(
  'vitals_notification_cycle',
  {
    userId: integer('user_id').primaryKey().notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'vitals_notification_cycle_user_id_fkey'
    })
  ]
);

export const visitSummaryUploadStatus = pgTable(
  'visit_summary_upload_status',
  {
    statusId: serial('status_id').primaryKey().notNull(),
    orderId: text('order_id').notNull(),
    userId: integer('user_id').notNull(),
    uploadType: enumVisitSummaryUploadStatusUploadType('upload_type').notNull(),
    uploadStatus: enumVisitSummaryUploadStatusUploadStatus('upload_status')
      .default('SUCCESS')
      .notNull(),
    error: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'visit_summary_upload_status_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'visit_summary_upload_status_user_id_fkey'
    })
  ]
);

export const vitalsPatientMonitoring = pgTable(
  'vitals_patient_monitoring',
  {
    patientId: integer('patient_id').notNull(),
    buserId: integer('buser_id').notNull(),
    groupId: serial('group_id').primaryKey().notNull(),
    startDate: timestamp('start_date', { withTimezone: true, mode: 'string' }),
    endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
    billed: boolean().default(false),
    billedDate: timestamp('billed_date', {
      withTimezone: true,
      mode: 'string'
    }),
    cost: numeric(),
    currency: text(),
    procedure: text().default(
      '{"CPT_codes":[{"code":"99454","description":"Device(s) supply with daily recording(s) or programmed alert(s) transmission, each 30 days"}]}'
    ),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'vitals_patient_monitoring_buser_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [users.userId],
      name: 'vitals_patient_monitoring_patient_id_fkey'
    })
  ]
);

export const userForms = pgTable(
  'user_forms',
  {
    userFormId: serial('user_form_id').primaryKey().notNull(),
    formId: integer('form_id'),
    assignedBy: integer('assigned_by'),
    assignedTo: integer('assigned_to'),
    doctorId: integer('doctor_id'),
    filledFormUrl: text('filled_form_url'),
    data: text(),
    status: varchar({ length: 255 }),
    score: numeric(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.assignedBy],
      foreignColumns: [users.userId],
      name: 'user_forms_assigned_by_fkey'
    }),
    foreignKey({
      columns: [table.assignedTo],
      foreignColumns: [users.userId],
      name: 'user_forms_assigned_to_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_forms_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.formId],
      foreignColumns: [forms.formId],
      name: 'user_forms_form_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_forms_order_id_fkey'
    })
  ]
);

export const userDiet = pgTable(
  'user_diet',
  {
    userDietId: serial('user_diet_id').primaryKey().notNull(),
    userId: integer('user_id'),
    mealType: enumUserDietMealType('meal_type').notNull(),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    imageUrl: text('image_url'),
    serving: integer(),
    servingUnit: varchar('serving_unit', { length: 255 }),
    food: text(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_diet_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_diet_user_id_fkey'
    })
  ]
);

export const telehealthServiceQuestionAnswer = pgTable(
  'telehealth_service_question_answer',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id'),
    questionId: integer('question_id'),
    answer: varchar({ length: 2000 }),
    otherText: varchar('other_text', { length: 2000 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.questionId],
      foreignColumns: [telehealthServiceQuestions.id],
      name: 'telehealth_service_question_answer_question_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'telehealth_service_question_answer_service_order_id_fkey'
    })
  ]
);

export const faxes = pgTable(
  'faxes',
  {
    faxId: serial('fax_id').primaryKey().notNull(),
    faxSid: text('fax_sid').notNull(),
    faxNumber: text('fax_number').notNull(),
    status: text().notNull(),
    referralId: integer('referral_id'),
    detail: text(),
    mediaUrl: text('media_url'),
    sentBy: integer('sent_by'),
    sentFor: integer('sent_for'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.referralId],
      foreignColumns: [referrals.referralId],
      name: 'faxes_referral_id_fkey'
    }),
    foreignKey({
      columns: [table.sentBy],
      foreignColumns: [users.userId],
      name: 'faxes_sent_by_fkey'
    }),
    foreignKey({
      columns: [table.sentFor],
      foreignColumns: [users.userId],
      name: 'faxes_sent_for_fkey'
    })
  ]
);

export const reviews = pgTable(
  'reviews',
  {
    reviewId: serial('review_id').primaryKey().notNull(),
    orderId: text('order_id'),
    review: text().notNull(),
    givenBy: integer('given_by'),
    givenTo: integer('given_to'),
    rating: integer(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    callQuality: integer('call_quality').default(0),
    isRecommendProvider: boolean('is_recommend_provider'),
    consultationRating: integer('consultation_rating').default(0),
    userComment: text('user_comment').default('')
  },
  (table) => [
    foreignKey({
      columns: [table.givenBy],
      foreignColumns: [users.userId],
      name: 'reviews_given_by_fkey'
    }),
    foreignKey({
      columns: [table.givenTo],
      foreignColumns: [users.userId],
      name: 'reviews_given_to_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'reviews_order_id_fkey'
    })
  ]
);

export const referrals = pgTable(
  'referrals',
  {
    referralId: serial('referral_id').primaryKey().notNull(),
    referredBy: integer('referred_by'),
    referredFor: integer('referred_for'),
    doctorId: integer('doctor_id'),
    type: text().notNull(),
    name: text(),
    path: text().notNull(),
    status: enumReferralsStatus(),
    orderId: integer('order_id'),
    detail: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    productId: integer('product_id'),
    prescriptionViewed: boolean('prescription_viewed').default(false),
    prescriptionProcessedBy: varchar('prescription_processed_by', {
      length: 100
    }),
    lifefileOrderId: integer('lifefile_order_id'),
    dosespotPrescriptionId: integer('dosespot_prescription_id')
  },
  (table) => [
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'referrals_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'referrals_order_id_fkey'
    }),
    foreignKey({
      columns: [table.referredBy],
      foreignColumns: [users.userId],
      name: 'referrals_referred_by_fkey'
    }),
    foreignKey({
      columns: [table.referredFor],
      foreignColumns: [users.userId],
      name: 'referrals_referred_for_fkey'
    }),
    foreignKey({
      columns: [table.productId],
      foreignColumns: [products.productId],
      name: 'referrals_product_id_fkey'
    })
  ]
);

export const transcriptions = pgTable(
  'transcriptions',
  {
    transcriptionId: serial('transcription_id').primaryKey().notNull(),
    orderId: text('order_id'),
    tuserId: integer('tuser_id'),
    status: text(),
    transcript: text(),
    updatedTranscript: text('updated_transcript'),
    audioStream: text('audio_stream'),
    archiveId: varchar('archive_id', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.orderId],
      name: 'transcriptions_order_id_fkey'
    }),
    foreignKey({
      columns: [table.tuserId],
      foreignColumns: [users.userId],
      name: 'transcriptions_tuser_id_fkey'
    })
  ]
);

export const userVitals = pgTable(
  'user_vitals',
  {
    userVitalId: serial('user_vital_id').primaryKey().notNull(),
    userId: integer('user_id'),
    value: text(),
    metric: varchar({ length: 255 }),
    detail: text(),
    type: varchar({ length: 255 }),
    displayName: varchar('display_name', { length: 255 }),
    mode: enumUserVitalsMode().default('automated'),
    billed: boolean().default(false),
    procedure: text(),
    abnormal: boolean().default(false),
    entityId: integer('entity_id'),
    bundleId: varchar('bundle_id', { length: 255 }),
    date: timestamp({ withTimezone: true, mode: 'string' }),
    description: text(),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    currency: text(),
    cost: numeric(),
    sourcePlatform: varchar('source_platform', { length: 255 })
  },
  (table) => [
    index('idx_user_vitals_user_id_date').using(
      'btree',
      table.userId.asc().nullsLast().op('int4_ops'),
      table.date.desc().nullsFirst().op('int4_ops')
    ),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_vitals_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_vitals_user_id_fkey'
    })
  ]
);

export const providerRatings = pgTable(
  'provider_ratings',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    providerId: integer('provider_id'),
    userId: integer('user_id'),
    rating: integer().default(1),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'provider_ratings_order_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [users.userId],
      name: 'provider_ratings_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'provider_ratings_user_id_fkey'
    })
  ]
);

export const schedules = pgTable(
  'schedules',
  {
    scheduleId: serial('schedule_id').primaryKey().notNull(),
    scheduledWith: integer('scheduled_with'),
    scheduledBy: integer('scheduled_by'),
    startYear: integer('start_year').notNull(),
    startMonth: integer('start_month').notNull(),
    startDay: integer('start_day').notNull(),
    endYear: integer('end_year').notNull(),
    endMonth: integer('end_month').notNull(),
    endDay: integer('end_day').notNull(),
    start: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    end: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    detail: text(),
    patientHistory: text('patient_history'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    orderGuid: varchar('order_guid', { length: 50 }).default(sql`NULL`),
    releaseMedical: boolean('release_medical').default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    orderId: integer('order_id')
  },
  (table) => [
    foreignKey({
      columns: [table.orderGuid],
      foreignColumns: [telehealthServiceOrder.orderGuid],
      name: 'schedules_order_guid_fkey'
    }),
    foreignKey({
      columns: [table.scheduledBy],
      foreignColumns: [users.userId],
      name: 'schedules_scheduled_by_fkey'
    }),
    foreignKey({
      columns: [table.scheduledWith],
      foreignColumns: [users.userId],
      name: 'schedules_scheduled_with_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'schedules_order_id_fkey'
    })
  ]
);

export const tokboxArchiveType = pgTable('tokbox_archive_type', {
  sessionId: text('session_id').primaryKey().notNull(),
  archiveId: text('archive_id'),
  type: text().notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const healthSummaryMetadata = pgTable('health_summary_metadata', {
  metadataId: serial('metadata_id').primaryKey().notNull(),
  category: text().notNull(),
  responseType: text('response_type'),
  template: text(),
  arrayFields: text('array_fields'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

export const healthSummariesLog = pgTable(
  'health_summaries_log',
  {
    logId: serial('log_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    success: boolean().default(false),
    summaryDetails: text('summary_details'),
    step: varchar({ length: 255 }),
    message: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'health_summaries_log_user_id_fkey'
    })
  ]
);

export const conversationMessages = pgTable(
  'conversation_messages',
  {
    cmId: serial('cm_id').primaryKey().notNull(),
    message: text(),
    userId: integer('user_id'),
    cId: integer('c_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.cId],
      foreignColumns: [conversations.cId],
      name: 'conversation_messages_c_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'conversation_messages_user_id_fkey'
    })
  ]
);

export const conversations = pgTable(
  'conversations',
  {
    cId: serial('c_id').primaryKey().notNull(),
    userOne: integer('user_one'),
    userTwo: integer('user_two'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userOne],
      foreignColumns: [users.userId],
      name: 'conversations_user_one_fkey'
    }),
    foreignKey({
      columns: [table.userTwo],
      foreignColumns: [users.userId],
      name: 'conversations_user_two_fkey'
    })
  ]
);

export const prescriptionTransferMedications = pgTable(
  'prescription_transfer_medications',
  {
    medicationId: serial('medication_id').primaryKey().notNull(),
    requestId: integer('request_id'),
    name: text().notNull(),
    isFulfilled: boolean('is_fulfilled').default(false),
    hgRxId: integer('hg_rx_id'),
    status: enumPrescriptionTransferMedicationsStatus().default('pending'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.requestId],
      foreignColumns: [prescriptionTransferRequest.requestId],
      name: 'prescription_transfer_medications_request_id_fkey'
    })
  ]
);

export const prescriptionTransferRequest = pgTable(
  'prescription_transfer_request',
  {
    requestId: serial('request_id').primaryKey().notNull(),
    userId: integer('user_id'),
    zipcode: text().notNull(),
    pharmacyName: text('pharmacy_name').notNull(),
    pharmacyAddress: text('pharmacy_address').notNull(),
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    transferAll: boolean('transfer_all').default(false).notNull(),
    phone: text().notNull(),
    dob: text().notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'prescription_transfer_request_user_id_fkey'
    })
  ]
);

export const refillRequest = pgTable(
  'refill_request',
  {
    id: serial().primaryKey().notNull(),
    serviceOrderId: integer('service_order_id').notNull(),
    drugDetails: varchar('drug_details', { length: 5000 }).notNull(),
    prescriptionImages: varchar('prescription_images', {
      length: 5000
    }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    index('refill_request_drug_details').using(
      'btree',
      table.drugDetails.asc().nullsLast().op('text_ops')
    ),
    foreignKey({
      columns: [table.serviceOrderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'refill_request_service_order_id_fkey'
    })
  ]
);

export const telehealthServiceStateMapping = pgTable(
  'telehealth_service_state_mapping',
  {
    id: serial().primaryKey().notNull(),
    stateId: integer('state_id').notNull(),
    serviceId: integer('service_id'),
    status: boolean().default(true),
    serviceType: enumTelehealthServiceStateMappingServiceType('service_type')
  },
  (table) => [
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'telehealth_service_state_mapping_state_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'telehealth_service_state_mapping_service_id_fkey'
    })
  ]
);

export const states = pgTable('states', {
  stateId: serial('state_id').primaryKey().notNull(),
  name: varchar({ length: 50 }).notNull(),
  abbreviation: varchar({ length: 2 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  isAsync: boolean('is_async').default(true).notNull()
});

export const drugsCategory = pgTable('drugs_category', {
  categoryId: serial('category_id').primaryKey().notNull(),
  categoryName: varchar('category_name', { length: 200 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false)
});

export const webhooksLog = pgTable('webhooks_log', {
  webhooksLogId: serial('webhooks_log_id').primaryKey().notNull(),
  body: varchar({ length: 5000 }),
  url: varchar({ length: 255 }).notNull(),
  responseStatus: varchar('response_status', { length: 255 }),
  caseId: varchar('case_id', { length: 255 }).notNull(),
  status: enumWebhooksLogStatus(),
  failedMessage: text('failed_message'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  type: varchar({ length: 255 }),
  headers: text(),
  actionType: enumWebhooksLogActionType('action_type')
});

export const paymentDetails = pgTable('payment_details', {
  paymentDetailsId: serial('payment_details_id').primaryKey().notNull(),
  paymentGateway: enumPaymentDetailsPaymentGateway('payment_gateway').notNull(),
  paymentGatewayCode: varchar('payment_gateway_code', { length: 255 }),
  paymentGatewayDetails: text('payment_gateway_details'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  tennantId: integer('tennant_id'),
  siteId: varchar('site_id', { length: 255 }).default(sql`NULL`)
});

export const pharmacyStateServiceMapping = pgTable(
  'pharmacy_state_service_mapping',
  {
    id: serial().primaryKey().notNull(),
    serviceId: integer('service_id'),
    stateId: integer('state_id'),
    pharmacyId: integer('pharmacy_id'),
    status: boolean().default(true).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull()
  },
  (table) => [
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'pharmacy_state_service_mapping_service_id_fkey'
    }),
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'pharmacy_state_service_mapping_state_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [pharmacies.pharmacyId],
      name: 'pharmacy_state_service_mapping_pharmacy_id_fkey'
    })
  ]
);

export const userInsurance = pgTable(
  'user_insurance',
  {
    userInsuranceId: serial('user_insurance_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    insuranceMemberId: varchar('insurance_member_id', {
      length: 255
    }).notNull(),
    insurancePlanName: varchar('insurance_plan_name', {
      length: 255
    }).notNull(),
    payerIdentification: varchar('payer_identification', {
      length: 255
    }).notNull(),
    coverType: varchar('cover_type', { length: 255 }).notNull(),
    userInsuranceIdFront: varchar('user_insurance_id_front', { length: 255 }),
    userInsuranceIdBack: varchar('user_insurance_id_back', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_insurance_user_id_fkey'
    })
  ]
);

export const insuranceEligibilityLogs = pgTable(
  'insurance_eligibility_logs',
  {
    insuranceEligibilityLogId: serial('insurance_eligibility_log_id')
      .primaryKey()
      .notNull(),
    orderId: integer('order_id').notNull(),
    insuranceMemberId: varchar('insurance_member_id', {
      length: 255
    }).notNull(),
    fullResponse: text('full_response').notNull(),
    isEligible: boolean('is_eligible').notNull(),
    ineligibleReason: text('ineligible_reason'),
    benefitType: varchar('benefit_type', { length: 255 }),
    benefitAmount: varchar('benefit_amount', { length: 255 }),
    benefitPercentage: varchar('benefit_percentage', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'insurance_eligibility_logs_order_id_fkey'
    })
  ]
);

export const userExternalIdMapping = pgTable(
  'user_external_id_mapping',
  {
    userExternalIdMappingId: serial('user_external_id_mapping_id')
      .primaryKey()
      .notNull(),
    userId: integer('user_id').notNull(),
    externalId: varchar('external_id', { length: 255 }).notNull(),
    externalIdSource: varchar('external_id_source', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_external_id_mapping_user_id_fkey'
    })
  ]
);

export const userFiles = pgTable(
  'user_files',
  {
    userFileId: serial('user_file_id').primaryKey().notNull(),
    userId: integer('user_id'),
    createdBy: integer('created_by'),
    doctorId: integer('doctor_id'),
    name: varchar({ length: 255 }),
    path: varchar({ length: 255 }),
    orderId: integer('order_id'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    type: enumUserFilesType(),
    fileKey: varchar('file_key', { length: 255 }),
    tenantId: integer('tenant_id')
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [users.userId],
      name: 'user_files_created_by_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [users.userId],
      name: 'user_files_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [orders.id],
      name: 'user_files_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_files_user_id_fkey'
    }),
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'user_files_tenant_id_fkey'
    })
  ]
);

export const consultNoteTemplates = pgTable(
  'consult_note_templates',
  {
    id: serial().primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    title: varchar({ length: 150 }).default(''),
    icdCode: jsonb('icd_code').default([]),
    cptCode: jsonb('cpt_code').default([]),
    subjective: text().default(''),
    objective: text().default(''),
    assessment: text().default(''),
    plan: text().default(''),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'consult_note_templates_user_id_fkey'
    })
  ]
);

export const tenantFiles = pgTable(
  'tenant_files',
  {
    id: serial().primaryKey().notNull(),
    tenantId: integer('tenant_id').notNull(),
    serviceId: integer('service_id'),
    key: varchar({ length: 255 }),
    disclaimerFile: json('disclaimer_file'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.tenantId],
      foreignColumns: [tennantMaster.id],
      name: 'tenant_files_tenant_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [telehealthServices.id],
      name: 'tenant_files_service_id_fkey'
    })
  ]
);

export const telehealthServiceCategory = pgTable(
  'telehealth_service_category',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 200 }).notNull(),
    slug: varchar({ length: 200 }).notNull(),
    title: varchar({ length: 200 }),
    subtitle: varchar({ length: 500 }),
    icon: varchar({ length: 500 }).notNull(),
    filterDisplay: boolean('filter_display').default(true).notNull(),
    serviceCategoryGuid: uuid('service_category_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    tennantId: integer('tennant_id'),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    updatedAt: timestamp('updated_at', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [tennantMaster.id],
      name: 'telehealth_service_category_tennant_id_fkey'
    })
  ]
);

export const userLicenses = pgTable(
  'user_licenses',
  {
    userLicenseId: serial('user_license_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    number: varchar({ length: 255 }).notNull(),
    stateId: integer('state_id').notNull(),
    state: varchar({ length: 255 }).notNull(),
    endDate: timestamp('end_date', {
      withTimezone: true,
      mode: 'string'
    }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean(),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_licenses_user_id_fkey'
    }),
    foreignKey({
      columns: [table.stateId],
      foreignColumns: [states.stateId],
      name: 'user_licenses_state_id_fkey'
    })
  ]
);

export const notificationReminder = pgTable(
  'notification_reminder',
  {
    id: serial().primaryKey().notNull(),
    orderId: integer('order_id'),
    userId: integer('user_id'),
    lastReminderDate: timestamp('last_reminder_date', {
      withTimezone: true,
      mode: 'string'
    }),
    reminderCount: integer('reminder_count').default(0).notNull(),
    notificationForType: enumNotificationReminderNotificationForType(
      'notification_for_type'
    )
      .default('intake_questionnaire')
      .notNull(),
    notificationStatus: enumNotificationReminderNotificationStatus(
      'notification_status'
    )
      .default('pending')
      .notNull(),
    errorMessage: text('error_message'),
    orderGuid: varchar('order_guid', { length: 50 }),
    userGuid: varchar('user_guid', { length: 50 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [telehealthServiceOrder.id],
      name: 'notification_reminder_order_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'notification_reminder_user_id_fkey'
    })
  ]
);

export const userPracticeGroups = pgTable(
  'user_practice_groups',
  {
    userId: integer('user_id').notNull(),
    practiceGroupId: integer('practice_group_id').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.practiceGroupId],
      foreignColumns: [practiceGroups.practiceGroupId],
      name: 'user_practice_groups_practice_group_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_practice_groups_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.practiceGroupId],
      name: 'user_practice_groups_pkey'
    })
  ]
);

export const scheduleTranslation = pgTable(
  'schedule_translation',
  {
    scheduleId: integer('schedule_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    patientHistory: text('patient_history'),
    detail: text()
  },
  (table) => [
    foreignKey({
      columns: [table.scheduleId],
      foreignColumns: [schedules.scheduleId],
      name: 'schedule_translation_schedule_id_fkey'
    }),
    primaryKey({
      columns: [table.scheduleId, table.languageCode],
      name: 'schedule_translation_pkey'
    })
  ]
);

export const requestTranslation = pgTable(
  'request_translation',
  {
    requestId: integer('request_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    message: text(),
    patientHistory: text('patient_history'),
    detail: text()
  },
  (table) => [
    foreignKey({
      columns: [table.requestId],
      foreignColumns: [requests.requestId],
      name: 'request_translation_request_id_fkey'
    }),
    primaryKey({
      columns: [table.requestId, table.languageCode],
      name: 'request_translation_pkey'
    })
  ]
);

export const usersTranslation = pgTable(
  'users_translation',
  {
    userId: integer('user_id').notNull(),
    languageCode: varchar('language_code', { length: 255 }).notNull(),
    firstName: text('first_name'),
    lastName: text('last_name'),
    history: text(),
    questionnaire: text()
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'users_translation_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.languageCode],
      name: 'users_translation_pkey'
    })
  ]
);

export const userViewers = pgTable(
  'user_viewers',
  {
    userId: integer('user_id').notNull(),
    viewerId: integer('viewer_id').notNull(),
    userFavorite: boolean('user_favorite').default(false),
    viewerFavorite: boolean('viewer_favorite').default(false),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_viewers_user_id_fkey'
    }),
    foreignKey({
      columns: [table.viewerId],
      foreignColumns: [users.userId],
      name: 'user_viewers_viewer_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.viewerId],
      name: 'user_viewers_pkey'
    })
  ]
);

export const userAssociations = pgTable(
  'user_associations',
  {
    userId: integer('user_id').notNull(),
    buserId: integer('buser_id').notNull(),
    userFavorite: boolean('user_favorite').default(false),
    buserFavorite: boolean('buser_favorite').default(false),
    maManageOrders: boolean('ma_manage_orders').default(false),
    isNotifyOnCapture: boolean('is_notify_on_capture').default(false),
    isRpmEnabled: boolean('is_rpm_enabled').default(false),
    isCustomizedVitalsThresholds: boolean(
      'is_customized_vitals_thresholds'
    ).default(false),
    vitalsThresholds: text('vitals_thresholds'),
    vitalsCron: text('vitals_cron'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.buserId],
      foreignColumns: [users.userId],
      name: 'user_associations_buser_id_fkey'
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.userId],
      name: 'user_associations_user_id_fkey'
    }),
    primaryKey({
      columns: [table.userId, table.buserId],
      name: 'user_associations_pkey'
    })
  ]
);
export const dashboardTennantList = pgView('dashboard_tennant_list', {
  id: integer(),
  tennantName: varchar('tennant_name', { length: 255 }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  asyncConsultCount: bigint('async_consult_count', { mode: 'number' }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  syncConsultCount: bigint('sync_consult_count', { mode: 'number' }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  totalConsultCount: bigint('total_consult_count', { mode: 'number' })
}).as(
  sql`SELECT tennant_master.id, tennant_master.tennant_name, sum( CASE WHEN telehealth_service_order.service_type = 'ASYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS async_consult_count, sum( CASE WHEN telehealth_service_order.service_type = 'SYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS sync_consult_count, count(telehealth_service_order.id) AS total_consult_count FROM tennant_master JOIN users ON tennant_master.id = users.tennant_id JOIN telehealth_service_order ON users.user_id = telehealth_service_order.answer_given_by GROUP BY tennant_master.id, tennant_master.tennant_name ORDER BY tennant_master.id`
);

export const dashboardTennantListMv = pgMaterializedView(
  'dashboard_tennant_list_mv',
  {
    id: integer(),
    tennantName: varchar('tennant_name', { length: 255 }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    asyncConsultCount: bigint('async_consult_count', { mode: 'number' }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    syncConsultCount: bigint('sync_consult_count', { mode: 'number' }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    totalConsultCount: bigint('total_consult_count', { mode: 'number' })
  }
).as(
  sql`SELECT tennant_master.id, tennant_master.tennant_name, sum( CASE WHEN telehealth_service_order.service_type = 'ASYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS async_consult_count, sum( CASE WHEN telehealth_service_order.service_type = 'SYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS sync_consult_count, count(telehealth_service_order.id) AS total_consult_count FROM tennant_master JOIN users ON tennant_master.id = users.tennant_id JOIN telehealth_service_order ON users.user_id = telehealth_service_order.answer_given_by GROUP BY tennant_master.id, tennant_master.tennant_name ORDER BY tennant_master.id`
);
